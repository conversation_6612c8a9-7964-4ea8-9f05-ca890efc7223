
import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Plus, X } from 'lucide-react';
import { Badge } from './ui/badge';

interface ManageTagsProps {
  type: 'Category' | 'Specialty';
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type Tag = {
  id: string;
  name: string;
};

const ManageTags = ({ type, open, onOpenChange }: ManageTagsProps) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const tableName = type === 'Category' ? 'categories' : 'specialties';
  const queryKey = type === 'Category' ? ['categories'] : ['allSpecialties'];

  const [newItem, setNewItem] = useState('');

  const { data: items = [], isLoading } = useQuery<Tag[]>({
    queryKey,
    queryFn: async () => {
      const { data, error } = await supabase.from(tableName).select('id, name').order('name');
      if (error) throw error;
      return data || [];
    },
  });

  const { mutate: addItem, isPending: isAdding } = useMutation({
    mutationFn: async (name: string) => {
      const { data, error } = await supabase.from(tableName).insert({ name }).select();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({ title: `${type} added` });
      queryClient.invalidateQueries({ queryKey });
      setNewItem('');
    },
    onError: (error: any) => {
      toast({ title: `Error adding ${type}`, description: error.message, variant: 'destructive' });
    },
  });

  const { mutate: deleteItem } = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from(tableName).delete().eq('id', id);
      if (error) throw error;
    },
    onSuccess: () => {
      toast({ title: `${type} deleted` });
      queryClient.invalidateQueries({ queryKey });
    },
    onError: (error: any) => {
      toast({ title: `Error deleting ${type}`, description: error.message, variant: 'destructive' });
    },
  });

  const handleAddItem = () => {
    if (newItem.trim() && !items.some(item => item.name.toLowerCase() === newItem.trim().toLowerCase())) {
      addItem(newItem.trim());
    } else if (newItem.trim()) {
      toast({ title: 'Item already exists', variant: 'destructive' });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Manage {type}s</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="flex items-center space-x-2">
            <Input
              value={newItem}
              onChange={(e) => setNewItem(e.target.value)}
              placeholder={`New ${type}`}
              onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}
            />
            <Button onClick={handleAddItem} disabled={isAdding}>
              {isAdding ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
              <span className="ml-2">Add</span>
            </Button>
          </div>
          <div className="space-y-2">
            <Label>Existing {type}s</Label>
            {isLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <div className="flex flex-wrap gap-2 border rounded-md p-2 min-h-[100px]">
                {items.map((item) => (
                  <Badge key={item.id} variant="secondary">
                    {item.name}
                    <button
                      onClick={() => deleteItem(item.id)}
                      className="ml-2 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    >
                      <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ManageTags;
