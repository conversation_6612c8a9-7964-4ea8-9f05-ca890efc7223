
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Proposal, ProposalTemplateSettings } from '../types/proposal';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { ProposalPDFTemplate } from '../components/ProposalPDFTemplate';
import { supabase } from '@/integrations/supabase/client';

export const generateProposalPDF = async (
  proposal: Proposal,
  templateSettings: ProposalTemplateSettings | null | undefined
): Promise<string> => {
  // Create a temporary div for rendering
  const tempDiv = document.createElement('div');
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px'; // Position off-screen
  tempDiv.style.width = '210mm'; // A4 width
  
  document.body.appendChild(tempDiv);

  // Create a root for React 18
  const root = createRoot(tempDiv);

  try {
    // Render the React component
    await new Promise<void>((resolve) => {
      root.render(React.createElement(ProposalPDFTemplate, { proposal }));
      // Give time for React to render
      setTimeout(resolve, 1000);
    });

    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = 210;
    const pdfHeight = 297;

    const pages = tempDiv.querySelectorAll<HTMLElement>('.pdf-page');

    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      if (i > 0) {
        pdf.addPage();
      }
      
      // Allow images to load
      await new Promise(r => setTimeout(r, 500));

      const canvas = await html2canvas(page, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: null,
      });

      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight, undefined, 'FAST');
    }

    // Download the PDF for the user
    const fileName = `speaker-proposal-${proposal.event.eventName.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}.pdf`;
    pdf.save(fileName);

    // Upload the PDF to Supabase Storage
    const pdfBlob = pdf.output('blob');
    const filePath = `proposals/${proposal.id}/${fileName}`;
    
    const { error: uploadError } = await supabase.storage
      .from('proposals')
      .upload(filePath, pdfBlob, {
        contentType: 'application/pdf',
        upsert: true,
      });

    if (uploadError) {
      throw uploadError;
    }

    return filePath;

  } catch (error) {
    console.error("Error generating or uploading PDF:", error);
    throw error;
  } finally {
    // Clean up
    root.unmount();
    if (document.body.contains(tempDiv)) {
      document.body.removeChild(tempDiv);
    }
  }
};
