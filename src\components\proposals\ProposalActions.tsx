
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, FileText, Send, Check, X } from "lucide-react";

interface ProposalActionsProps {
  proposal: {
    id: string;
    status: string;
    event_name: string;
    created_at: string;
    details: any;
    pdf_path?: string;
  };
}

export const ProposalActions = ({ proposal }: ProposalActionsProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Badge className={getStatusColor(proposal.status || 'draft')}>
        {proposal.status || 'draft'}
      </Badge>
      
      {proposal.status === 'draft' && (
        <Button size="sm" variant="outline">
          <Send className="h-4 w-4 mr-1" />
          Send
        </Button>
      )}
      
      <Button size="sm" variant="outline">
        <FileText className="h-4 w-4 mr-1" />
        Generate PDF
      </Button>
      
      {proposal.pdf_path && (
        <Button size="sm" variant="outline">
          <Download className="h-4 w-4 mr-1" />
          Download
        </Button>
      )}
    </div>
  );
};
