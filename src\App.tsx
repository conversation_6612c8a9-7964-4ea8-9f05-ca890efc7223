
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { ThemeProvider } from "./components/theme-provider";
import ProtectedRoute from "./components/ProtectedRoute";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Profile from "./pages/Profile";
import Speakers from "./pages/Speakers";
import SpeakerDetail from "./pages/SpeakerDetail";
import EditSpeaker from "./pages/EditSpeaker";
import Events from "./pages/Events";
import EventDetail from "./pages/EventDetail";
import Calendar from "./pages/Calendar";
import Employees from "./pages/Employees";
import EmployeeDetail from "./pages/EmployeeDetail";
import Proposals from "./pages/Proposals";
import BitrixIntegration from "./pages/BitrixIntegration";
import BirthdayManagement from "./pages/BirthdayManagement";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
        <TooltipProvider>
          <AuthProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <Index />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <ProtectedRoute>
                      <Profile />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/speakers"
                  element={
                    <ProtectedRoute>
                      <Speakers />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/speaker/:id"
                  element={
                    <ProtectedRoute>
                      <SpeakerDetail />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/speaker/:id/edit"
                  element={
                    <ProtectedRoute>
                      <EditSpeaker />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/events"
                  element={
                    <ProtectedRoute>
                      <Events />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/event/:id"
                  element={
                    <ProtectedRoute>
                      <EventDetail />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/calendar"
                  element={
                    <ProtectedRoute>
                      <Calendar />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/employees"
                  element={
                    <ProtectedRoute>
                      <Employees />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/employee/:id"
                  element={
                    <ProtectedRoute>
                      <EmployeeDetail />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/proposals"
                  element={
                    <ProtectedRoute>
                      <Proposals />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/proposals/:id"
                  element={
                    <ProtectedRoute>
                      <Proposals />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/bitrix"
                  element={
                    <ProtectedRoute>
                      <BitrixIntegration />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/birthdays"
                  element={
                    <ProtectedRoute>
                      <BirthdayManagement />
                    </ProtectedRoute>
                  }
                />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </AuthProvider>
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
