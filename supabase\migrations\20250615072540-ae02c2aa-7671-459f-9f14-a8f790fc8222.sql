
-- ========= SPEAKERS MIGRATION =========

-- Add missing columns to speakers table to match local data structure
ALTER TABLE public.speakers ADD COLUMN IF NOT EXISTS phone TEXT;
ALTER TABLE public.speakers ADD COLUMN IF NOT EXISTS location TEXT;
ALTER TABLE public.speakers ADD COLUMN IF NOT EXISTS experience TEXT;

-- Rename 'image_url' to 'image' for consistency with frontend code if it exists
DO $$
BEGIN
  IF EXISTS(SELECT * FROM information_schema.columns WHERE table_schema = 'public' AND table_name='speakers' AND column_name='image_url')
  THEN
    ALTER TABLE public.speakers RENAME COLUMN image_url TO image;
  ELSIF NOT EXISTS(SELECT * FROM information_schema.columns WHERE table_schema = 'public' AND table_name='speakers' AND column_name='image')
  THEN
    ALTER TABLE public.speakers ADD COLUMN image TEXT;
  END IF;
END $$;


-- Rename 'topics' to 'specialties' for consistency with frontend code if it exists
DO $$
BEGIN
  IF EXISTS(SELECT * FROM information_schema.columns WHERE table_schema = 'public' AND table_name='speakers' AND column_name='topics')
  THEN
    ALTER TABLE public.speakers RENAME COLUMN topics TO specialties;
  ELSIF NOT EXISTS(SELECT * FROM information_schema.columns WHERE table_schema = 'public' AND table_name='speakers' AND column_name='specialties')
  THEN
    ALTER TABLE public.speakers ADD COLUMN specialties TEXT[];
  END IF;
END $$;

-- Clear existing speakers to avoid duplicates on re-run, and restart id sequence
-- CASCADE will also clear related proposals
TRUNCATE public.speakers RESTART IDENTITY CASCADE;

-- Seed speakers table with initial data
INSERT INTO public.speakers (name, bio, category, image, rate, email, phone, location, experience, specialties, availability) VALUES
('Dr. Sarah Chen', 'AI researcher and tech entrepreneur with 15+ years in machine learning and data science.', 'Technology', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face', 25000, '<EMAIL>', '+****************', 'San Francisco, CA', '15+ years in AI and Machine Learning', '{"Artificial Intelligence", "Machine Learning", "Data Science", "Tech Innovation"}', 'Available'),
('Marcus Rodriguez', 'Former Google VP of Engineering, now startup advisor and cybersecurity expert.', 'Technology', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face', 30000, '<EMAIL>', '+****************', 'Austin, TX', '20+ years in Technology Leadership', '{"Cybersecurity", "Cloud Computing", "Digital Transformation", "Tech Leadership"}', 'Available'),
('Lisa Park', 'Blockchain pioneer and cryptocurrency expert, founder of three successful fintech startups.', 'Technology', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face', 22000, '<EMAIL>', '+****************', 'New York, NY', '12+ years in Blockchain and Fintech', '{"Blockchain", "Cryptocurrency", "Fintech", "Digital Innovation"}', 'Busy'),
('James Wellington', 'Fortune 500 CEO and leadership consultant, author of ''Leading in the Digital Age''.', 'Business', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face', 35000, '<EMAIL>', '+1 (555) 456-7890', 'Chicago, IL', '25+ years in Executive Leadership', '{"Leadership", "Strategic Planning", "Digital Transformation", "Change Management"}', 'Available'),
('Amanda Foster', 'Sales performance expert and bestselling author who has trained over 50,000 sales professionals.', 'Business', 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face', 18000, '<EMAIL>', '+1 (555) 567-8901', 'Miami, FL', '18+ years in Sales Training', '{"Sales Strategy", "Team Building", "Performance Management", "Customer Relations"}', 'Available'),
('Robert Kim', 'Startup mentor and venture capitalist, has helped launch over 100 successful companies.', 'Business', 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face', 28000, '<EMAIL>', '+1 (555) 678-9012', 'Silicon Valley, CA', '20+ years in Venture Capital', '{"Entrepreneurship", "Startup Strategy", "Investment", "Business Development"}', 'Unavailable'),
('Dr. Michelle Adams', 'Wellness expert and mindfulness coach, helping organizations build healthier work cultures.', 'Health & Wellness', 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400&h=400&fit=crop&crop=face', 15000, '<EMAIL>', '+1 (555) 789-0123', 'Portland, OR', '12+ years in Corporate Wellness', '{"Mindfulness", "Stress Management", "Work-Life Balance", "Mental Health"}', 'Available'),
('Dr. Carlos Martinez', 'Sports psychologist and performance coach for Olympic athletes and corporate teams.', 'Health & Wellness', 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=400&fit=crop&crop=face', 20000, '<EMAIL>', '+1 (555) 890-1234', 'Denver, CO', '15+ years in Sports Psychology', '{"Performance Psychology", "Team Dynamics", "Goal Setting", "Resilience"}', 'Available'),
('Prof. Rachel Thompson', 'Educational technology researcher and former university dean, expert in future of learning.', 'Education', 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=face', 16000, '<EMAIL>', '+1 (555) 901-2345', 'Boston, MA', '20+ years in Educational Leadership', '{"Educational Technology", "Learning Innovation", "Curriculum Development", "Student Engagement"}', 'Available'),
('David Chang', 'Skills development expert and corporate trainer, specializing in professional development programs.', 'Education', 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=400&fit=crop&crop=face', 14000, '<EMAIL>', '+****************', 'Seattle, WA', '14+ years in Professional Development', '{"Skills Training", "Professional Development", "Career Coaching", "Workplace Learning"}', 'Busy'),
('Maya Patel', 'Inspirational speaker and life coach, overcame adversity to become a successful entrepreneur.', 'Motivational', 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face', 12000, '<EMAIL>', '+****************', 'Phoenix, AZ', '10+ years in Motivational Speaking', '{"Personal Growth", "Overcoming Adversity", "Goal Achievement", "Self-Motivation"}', 'Available'),
('Tony Richardson', 'Former Navy SEAL turned motivational speaker, expert in building mental toughness and resilience.', 'Motivational', 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face', 17000, '<EMAIL>', '+****************', 'Nashville, TN', '8+ years in Motivational Speaking', '{"Mental Toughness", "Resilience", "Team Building", "Leadership Under Pressure"}', 'Available');

-- ========= EVENTS MIGRATION =========

-- Add missing columns to events table to match frontend data structure
-- Note: 'date' column already exists as timestamptz
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS "startTime" TEXT;
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS "endTime" TEXT;
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS "speakerIds" UUID[];
