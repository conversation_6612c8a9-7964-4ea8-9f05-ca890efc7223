
import { speakers } from "@/data/speakers";
import { getEvents } from "./eventStorage";

export const updateSpeakerAvailability = () => {
  const events = getEvents();
  const today = new Date();

  // Reset all speakers to available first
  speakers.forEach(speaker => {
    speaker.availability = 'Available';
  });

  // Check for speakers with upcoming events
  events.forEach(event => {
    if (new Date(event.date) >= today && event.status === 'Scheduled') {
      event.speakerIds.forEach(speakerId => {
        const speaker = speakers.find(s => s.id === speakerId);
        if (speaker) {
          speaker.availability = 'Busy';
        }
      });
    }
  });
};
