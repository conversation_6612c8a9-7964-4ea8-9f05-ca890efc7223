
-- Create a table to store customizable template settings for each user
CREATE TABLE public.proposal_template_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL UNIQUE DEFAULT auth.uid(),
  cover_page_title TEXT DEFAULT 'Speaker Proposal',
  cover_page_image_url TEXT DEFAULT 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=1200&h=800&fit=crop',
  about_us_mission TEXT DEFAULT 'At Speaker Agency, we connect world-class speakers with organizations seeking exceptional thought leadership and inspiration. Our mission is to deliver transformative experiences that educate, motivate, and drive meaningful change.',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security to ensure users can only edit their own template
ALTER TABLE public.proposal_template_settings ENABLE ROW LEVEL SECURITY;

-- Create policy for users to view their own template settings
CREATE POLICY "Users can view their own template settings"
  ON public.proposal_template_settings
  FOR SELECT
  USING (auth.uid() = user_id);

-- Create policy for users to create their own template settings
CREATE POLICY "Users can insert their own template settings"
  ON public.proposal_template_settings
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create policy for users to update their own template settings
CREATE POLICY "Users can update their own template settings"
  ON public.proposal_template_settings
  FOR UPDATE
  USING (auth.uid() = user_id);
