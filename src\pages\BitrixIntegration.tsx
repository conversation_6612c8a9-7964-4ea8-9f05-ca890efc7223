
import { useState, useEffect } from "react";
import Layout from "../components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, Users, RefreshCw, ExternalLink, CheckCircle, XCircle, Clock } from "lucide-react";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import { Tables } from "@/integrations/supabase/types";

type BitrixDeal = Tables<'bitrix_deals'>;

const BitrixIntegration = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data: deals = [], isLoading, error, refetch } = useQuery({
    queryKey: ['bitrix-deals'],
    queryFn: async (): Promise<BitrixDeal[]> => {
      console.log('Fetching Bitrix deals...');
      const { data, error } = await supabase
        .from('bitrix_deals')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching deals:', error);
        throw new Error(error.message);
      }
      console.log('Fetched deals:', data?.length || 0);
      return data || [];
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const syncToEventMutation = useMutation({
    mutationFn: async (dealId: string) => {
      const deal = deals.find(d => d.id === dealId);
      if (!deal) throw new Error('Deal not found');

      if (!deal.event_date) {
        throw new Error('Deal must have an event date to sync');
      }

      // Create event from deal
      const { data: event, error } = await supabase
        .from('events')
        .insert({
          title: deal.deal_title,
          date: deal.event_date,
          startTime: '09:00',
          endTime: '17:00',
          location: deal.event_location,
          description: `Event created from Bitrix deal #${deal.bitrix_deal_id}\n\nClient: ${deal.client_name}\nRequirements: ${deal.speaker_requirements}`,
          status: 'Scheduled',
          speakerIds: []
        })
        .select()
        .single();

      if (error) throw error;

      // Update deal with synced event ID
      const { error: updateError } = await supabase
        .from('bitrix_deals')
        .update({ 
          synced_to_event_id: event.id, 
          sync_status: 'synced',
          updated_at: new Date().toISOString()
        })
        .eq('id', dealId);

      if (updateError) throw updateError;

      return event;
    },
    onSuccess: (event) => {
      queryClient.invalidateQueries({ queryKey: ['bitrix-deals'] });
      toast({
        title: "Event Created",
        description: `Deal has been synced to event successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Sync Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleRefresh = () => {
    refetch();
    toast({
      title: "Refreshing",
      description: "Checking for new deals from Bitrix...",
    });
  };

  const getSyncStatusColor = (status: string | null) => {
    switch (status) {
      case 'synced':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    }
  };

  const getSyncStatusIcon = (status: string | null) => {
    switch (status) {
      case 'synced':
        return CheckCircle;
      case 'failed':
        return XCircle;
      default:
        return Clock;
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (error) {
    console.error('Query error:', error);
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Bitrix CRM Integration</h1>
            <p className="text-muted-foreground mt-2">
              Manage deals from Bitrix CRM and sync them to events
            </p>
          </div>
          <Button
            onClick={handleRefresh}
            variant="outline"
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Deals</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{deals.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Synced to Events</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {deals.filter(d => d.sync_status === 'synced').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Sync</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {deals.filter(d => d.sync_status === 'pending').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <ExternalLink className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(deals.reduce((sum, deal) => sum + (deal.deal_amount || 0), 0))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Deals List */}
        <div className="space-y-4">
          {isLoading ? (
            <Card>
              <CardContent className="text-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                <p className="text-muted-foreground">Loading deals...</p>
              </CardContent>
            </Card>
          ) : error ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-destructive mb-2">Error loading deals: {error.message}</p>
                <Button onClick={handleRefresh} variant="outline">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          ) : deals.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  No deals found. Make sure your Bitrix webhook is configured correctly.
                </p>
                <div className="text-sm text-muted-foreground">
                  <p>Webhook URL should be: <code className="bg-muted px-2 py-1 rounded">
                    https://bpqwisbseevdcjsfnbhz.supabase.co/functions/v1/bitrix-webhook
                  </code></p>
                </div>
              </CardContent>
            </Card>
          ) : (
            deals.map((deal) => {
              const StatusIcon = getSyncStatusIcon(deal.sync_status);
              return (
                <Card key={deal.id}>
                  <CardHeader className="flex flex-row items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl">{deal.deal_title}</CardTitle>
                      <div className="flex flex-wrap items-center gap-4 mt-2 text-sm text-muted-foreground">
                        <span>Deal #{deal.bitrix_deal_id}</span>
                        {deal.client_name && <span>Client: {deal.client_name}</span>}
                        {deal.deal_amount && <span>Value: {formatCurrency(deal.deal_amount)}</span>}
                        {deal.deal_stage && <span>Stage: {deal.deal_stage}</span>}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getSyncStatusColor(deal.sync_status)}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {deal.sync_status || 'pending'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {deal.event_date && (
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>Event Date: {format(new Date(deal.event_date), "PPP")}</span>
                        </div>
                      )}
                      {deal.event_location && (
                        <div className="flex items-center space-x-2">
                          <span>Location: {deal.event_location}</span>
                        </div>
                      )}
                      {deal.speaker_requirements && (
                        <div>
                          <p className="text-sm text-muted-foreground">Requirements:</p>
                          <p className="text-sm">{deal.speaker_requirements}</p>
                        </div>
                      )}
                      <div className="flex justify-between items-center pt-2">
                        <span className="text-xs text-muted-foreground">
                          Last updated: {format(new Date(deal.updated_at), "PPp")}
                        </span>
                        <div className="flex space-x-2">
                          {deal.synced_to_event_id ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/event/${deal.synced_to_event_id}`)}
                            >
                              View Event
                            </Button>
                          ) : deal.event_date ? (
                            <Button
                              size="sm"
                              onClick={() => syncToEventMutation.mutate(deal.id)}
                              disabled={syncToEventMutation.isPending}
                            >
                              {syncToEventMutation.isPending ? 'Syncing...' : 'Sync to Event'}
                            </Button>
                          ) : (
                            <Button variant="outline" size="sm" disabled>
                              No Event Date
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      </div>
    </Layout>
  );
};

export default BitrixIntegration;
