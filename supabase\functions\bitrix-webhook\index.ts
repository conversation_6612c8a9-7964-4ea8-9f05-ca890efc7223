
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse the incoming webhook data
    const requestData = await req.json()
    console.log('Bitrix webhook received:', requestData)

    // Handle different webhook formats
    let event = requestData.event
    let data = requestData.data

    // If the webhook sends data directly without event wrapper
    if (!event && requestData.FIELDS) {
      event = 'ONCRMDEALADD'
      data = { FIELDS: requestData }
    }

    // Handle deal events
    if (event === 'ONCRMDEALADD' || event === 'ONCRMDEALUPDATE' || requestData.FIELDS) {
      const dealFields = data?.FIELDS || requestData.FIELDS || requestData
      const dealId = dealFields.ID || dealFields.id
      
      console.log('Processing deal:', dealId, dealFields)
      
      // Extract relevant information from the webhook data
      const dealInfo = {
        bitrix_deal_id: dealId?.toString() || 'unknown',
        deal_title: dealFields.TITLE || dealFields.title || 'Untitled Deal',
        client_name: dealFields.CONTACT_ID ? await getContactName(dealFields.CONTACT_ID) : (dealFields.CLIENT_NAME || null),
        client_email: dealFields.CONTACT_ID ? await getContactEmail(dealFields.CONTACT_ID) : (dealFields.CLIENT_EMAIL || null),
        deal_amount: dealFields.OPPORTUNITY ? parseFloat(dealFields.OPPORTUNITY) : (dealFields.DEAL_AMOUNT ? parseFloat(dealFields.DEAL_AMOUNT) : null),
        deal_stage: dealFields.STAGE_ID || dealFields.stage || 'NEW',
        event_date: dealFields.UF_CRM_EVENT_DATE || dealFields.EVENT_DATE || dealFields.event_date || null,
        event_location: dealFields.UF_CRM_EVENT_LOCATION || dealFields.EVENT_LOCATION || dealFields.event_location || null,
        speaker_requirements: dealFields.COMMENTS || dealFields.comments || dealFields.speaker_requirements || null,
        sync_status: 'pending'
      }

      console.log('Upserting deal info:', dealInfo)

      // Upsert the deal in our database
      const { data: existingDeal } = await supabase
        .from('bitrix_deals')
        .select('*')
        .eq('bitrix_deal_id', dealInfo.bitrix_deal_id)
        .single()

      if (existingDeal) {
        const { error: updateError } = await supabase
          .from('bitrix_deals')
          .update({ ...dealInfo, updated_at: new Date().toISOString() })
          .eq('bitrix_deal_id', dealInfo.bitrix_deal_id)

        if (updateError) {
          console.error('Error updating deal:', updateError)
          throw updateError
        }
        console.log('Deal updated successfully')
      } else {
        const { error: insertError } = await supabase
          .from('bitrix_deals')
          .insert(dealInfo)

        if (insertError) {
          console.error('Error inserting deal:', insertError)
          throw insertError
        }
        console.log('Deal inserted successfully')
      }

      // Auto-sync to events if deal is won and has event details
      if ((dealFields.STAGE_ID === 'WON' || dealFields.stage === 'WON') && dealInfo.event_date) {
        await syncDealToEvent(supabase, dealInfo.bitrix_deal_id, dealInfo)
      }
    }

    return new Response(
      JSON.stringify({ success: true, message: 'Webhook processed successfully' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error processing Bitrix webhook:', error)
    return new Response(
      JSON.stringify({ error: error.message, success: false }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

async function getContactName(contactId: string): Promise<string | null> {
  try {
    const bitrixUrl = Deno.env.get('BITRIX_WEBHOOK_URL')
    if (!bitrixUrl) return null

    const response = await fetch(`${bitrixUrl}/crm.contact.get?ID=${contactId}`)
    const data = await response.json()
    if (data.result) {
      return `${data.result.NAME || ''} ${data.result.LAST_NAME || ''}`.trim()
    }
  } catch (error) {
    console.error('Error fetching contact name:', error)
  }
  return null
}

async function getContactEmail(contactId: string): Promise<string | null> {
  try {
    const bitrixUrl = Deno.env.get('BITRIX_WEBHOOK_URL')
    if (!bitrixUrl) return null

    const response = await fetch(`${bitrixUrl}/crm.contact.get?ID=${contactId}`)
    const data = await response.json()
    if (data.result && data.result.EMAIL && data.result.EMAIL[0]) {
      return data.result.EMAIL[0].VALUE
    }
  } catch (error) {
    console.error('Error fetching contact email:', error)
  }
  return null
}

async function syncDealToEvent(supabase: any, dealId: string, dealInfo: any) {
  try {
    // Create event from deal
    const { data: event, error } = await supabase
      .from('events')
      .insert({
        title: dealInfo.deal_title,
        date: dealInfo.event_date,
        startTime: '09:00',
        endTime: '17:00',
        location: dealInfo.event_location,
        description: `Event created from Bitrix deal #${dealId}\n\nClient: ${dealInfo.client_name}\nRequirements: ${dealInfo.speaker_requirements}`,
        status: 'Scheduled',
        speakerIds: []
      })
      .select()
      .single()

    if (!error && event) {
      // Update deal with synced event ID
      await supabase
        .from('bitrix_deals')
        .update({ 
          synced_to_event_id: event.id, 
          sync_status: 'synced',
          updated_at: new Date().toISOString()
        })
        .eq('bitrix_deal_id', dealId)

      console.log(`Deal ${dealId} synced to event ${event.id}`)
    }
  } catch (error) {
    console.error('Error syncing deal to event:', error)
    // Update sync status to failed
    await supabase
      .from('bitrix_deals')
      .update({ 
        sync_status: 'failed',
        updated_at: new Date().toISOString()
      })
      .eq('bitrix_deal_id', dealId)
  }
}
