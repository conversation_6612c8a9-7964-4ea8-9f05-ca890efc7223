
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Speaker } from "@/types/speaker";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Users, DollarSign, Trash2 } from "lucide-react";
import ProposalSpeakerCard from "./ProposalSpeakerCard";

interface SpeakerSelectionProps {
  selectedSpeakers: any[];
  setSelectedSpeakers: (speakers: any[]) => void;
}

const SpeakerSelection = ({ selectedSpeakers, setSelectedSpeakers }: SpeakerSelectionProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // Fetch speakers from database
  const { data: speakers = [], isLoading } = useQuery({
    queryKey: ['speakers'],
    queryFn: async () => {
      const { data, error } = await supabase.from('speakers').select('*');
      if (error) throw error;
      return data || [];
    }
  });

  // Get unique categories
  const categories = Array.from(new Set(speakers.map(s => s.category).filter(Boolean)));

  // Filter speakers based on search and category
  const filteredSpeakers = speakers.filter(speaker => {
    const matchesSearch = speaker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         speaker.bio?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         speaker.specialties?.some((s: string) => s.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = !selectedCategory || speaker.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const handleSpeakerSelect = (speaker: Speaker, customRate?: number, profileImageUrl?: string) => {
    const speakerData = {
      ...speaker,
      rate: customRate || speaker.rate,
      image: profileImageUrl || speaker.image || undefined
    };

    const isAlreadySelected = selectedSpeakers.some(s => s.id === speaker.id);
    
    if (isAlreadySelected) {
      // Update existing speaker
      setSelectedSpeakers(selectedSpeakers.map(s => 
        s.id === speaker.id ? speakerData : s
      ));
    } else {
      // Add new speaker
      setSelectedSpeakers([...selectedSpeakers, speakerData]);
    }
  };

  const handleRemoveSpeaker = (speakerId: string) => {
    setSelectedSpeakers(selectedSpeakers.filter(s => s.id !== speakerId));
  };

  const totalCost = selectedSpeakers.reduce((sum, speaker) => sum + (speaker.rate || 0), 0);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="h-12 w-12 animate-pulse text-muted-foreground mx-auto mb-4" />
          <p>Loading speakers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Selected Speakers Summary */}
      {selectedSpeakers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Selected Speakers ({selectedSpeakers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {selectedSpeakers.map((speaker) => (
                <div key={speaker.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-3">
                    {speaker.image && (
                      <img src={speaker.image} alt={speaker.name} className="w-10 h-10 rounded-full object-cover" />
                    )}
                    <div>
                      <h4 className="font-medium">{speaker.name}</h4>
                      <p className="text-sm text-muted-foreground">{speaker.category}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">${(speaker.rate || 0).toLocaleString()}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveSpeaker(speaker.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              <div className="pt-3 border-t">
                <div className="flex items-center justify-between text-lg font-semibold">
                  <span className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Total Cost:
                  </span>
                  <span className="text-primary">${totalCost.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filter */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search speakers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedCategory === "" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory("")}
          >
            All Categories
          </Button>
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Available Speakers */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Available Speakers</h3>
        {filteredSpeakers.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Users className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No speakers found</h3>
              <p className="text-muted-foreground text-center">
                {searchTerm || selectedCategory 
                  ? "Try adjusting your search or filters" 
                  : "No speakers available in the database"}
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredSpeakers.map((speaker) => {
              // Convert database speaker to Speaker type with proper availability handling
              const speakerWithImage: Speaker = {
                ...speaker,
                availability: speaker.availability as string | null,
                image: undefined // Will be set via upload
              };
              
              return (
                <ProposalSpeakerCard
                  key={speaker.id}
                  speaker={speakerWithImage}
                  onSelect={handleSpeakerSelect}
                  isSelected={selectedSpeakers.some(s => s.id === speaker.id)}
                />
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default SpeakerSelection;
