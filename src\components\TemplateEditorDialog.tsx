import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { ProposalTemplateSettings } from '@/types/proposal';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  cover_page_title: z.string().min(1, 'Title is required'),
  cover_page_image_url: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  about_us_mission: z.string().min(1, 'Mission text is required').max(500, 'Mission text cannot exceed 500 characters'),
});

type TemplateFormValues = z.infer<typeof formSchema>;

interface TemplateEditorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TemplateEditorDialog({ open, onOpenChange }: TemplateEditorDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  
  const { data: settings, isLoading } = useQuery({
    queryKey: ['proposalTemplateSettings'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data, error } = await supabase
        .from('proposal_template_settings')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) throw new Error(error.message);
      return data;
    },
  });

  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cover_page_title: '',
      cover_page_image_url: '',
      about_us_mission: '',
    },
  });

  useEffect(() => {
    if (settings) {
      form.reset({
        cover_page_title: settings.cover_page_title,
        cover_page_image_url: settings.cover_page_image_url || '',
        about_us_mission: settings.about_us_mission,
      });
    }
  }, [settings, form]);

  const upsertMutation = useMutation({
    mutationFn: async (values: TemplateFormValues) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('You must be logged in to save settings.');

      const { data, error } = await supabase.from('proposal_template_settings').upsert({
        user_id: user.id,
        id: settings?.id,
        cover_page_title: values.cover_page_title,
        cover_page_image_url: values.cover_page_image_url,
        about_us_mission: values.about_us_mission,
        updated_at: new Date().toISOString(),
      }, { onConflict: 'user_id' }).select().single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['proposalTemplateSettings'] });
      toast({ title: 'Template saved successfully!' });
      onOpenChange(false);
    },
    onError: (error) => {
      toast({ title: 'Error saving template', description: error.message, variant: 'destructive' });
    },
  });

  const onSubmit = (values: TemplateFormValues) => {
    upsertMutation.mutate(values);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Proposal Template</DialogTitle>
          <DialogDescription>
            Customize the look and feel of your generated PDF proposals.
          </DialogDescription>
        </DialogHeader>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
              <FormField
                control={form.control}
                name="cover_page_title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cover Page Title</FormLabel>
                    <FormControl><Input placeholder="Speaker Proposal" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="cover_page_image_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cover Page Background Image</FormLabel>
                    {(field.value && !isUploading) && (
                      <div className="mt-2">
                          <img src={field.value} alt="Cover preview" className="rounded-md max-h-40 w-full object-cover" />
                      </div>
                    )}
                    {isUploading && (
                      <div className="flex items-center justify-center h-40 border rounded-md">
                        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                      </div>
                    )}
                    <FormControl>
                      <Input
                        type="file"
                        accept="image/*"
                        className="mt-2"
                        disabled={isUploading || upsertMutation.isPending}
                        onChange={async (e) => {
                          const file = e.target.files?.[0];
                          if (!file) return;

                          setIsUploading(true);
                          const { data: { user } } = await supabase.auth.getUser();
                          if (!user) {
                            toast({ title: 'Authentication Error', description: 'You must be logged in.', variant: 'destructive' });
                            setIsUploading(false);
                            return;
                          }

                          const filePath = `${user.id}/${Date.now()}_${file.name}`;
                          const { error: uploadError } = await supabase.storage
                            .from('template_images')
                            .upload(filePath, file);
                          
                          if (uploadError) {
                            toast({ title: 'Upload failed', description: uploadError.message, variant: 'destructive' });
                            setIsUploading(false);
                            return;
                          }

                          const { data: { publicUrl } } = supabase.storage
                            .from('template_images')
                            .getPublicUrl(filePath);
                          
                          field.onChange(publicUrl);
                          setIsUploading(false);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="about_us_mission"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>About Us / Mission Statement</FormLabel>
                    <FormControl><Textarea rows={5} placeholder="Our mission is to..." {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
                <Button type="submit" disabled={upsertMutation.isPending || isUploading}>
                  {(upsertMutation.isPending || isUploading) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save Changes
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
