
import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { 
  Gift, 
  Mail, 
  Calendar, 
  User, 
  Send, 
  Edit3, 
  Clock,
  CheckCircle2,
  AlertCircle,
  Cake,
  Settings,
  Save
} from "lucide-react";
import { format, isToday, isTomorrow } from "date-fns";
import { Speaker } from "@/types/speaker";

const BirthdayManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [emailTemplate, setEmailTemplate] = useState({
    subject: "Happy Birthday from Speaker Agency! 🎉",
    body: `Dear {speaker_name},

Happy Birthday! 🎂

On this special day, we want to take a moment to celebrate you and express our gratitude for being such an important part of our Speaker Agency family.

Your talent, dedication, and the incredible impact you make through your speaking engagements continue to inspire audiences everywhere. We feel fortunate to work with someone as exceptional as you.

We hope your birthday is filled with joy, laughter, and all the things that make you happiest. Here's to another year of success, growth, and amazing speaking opportunities!

Warmest birthday wishes,
The Speaker Agency Team

P.S. We'd love to help you celebrate! Let us know if you'd like to discuss any upcoming speaking opportunities or if there's anything we can do to support your goals in the year ahead.`
  });

  const [isEditingTemplate, setIsEditingTemplate] = useState(false);
  const [selectedSpeakers, setSelectedSpeakers] = useState<string[]>([]);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [stampMailbox, setStampMailbox] = useState("");

  // Fetch email settings
  const { data: emailSettings } = useQuery({
    queryKey: ['email-settings'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('email_settings')
        .select('*')
        .limit(1)
        .single();
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching email settings:', error);
      }
      
      return data;
    }
  });

  useEffect(() => {
    if (emailSettings?.stamp_mailbox) {
      setStampMailbox(emailSettings.stamp_mailbox);
    }
  }, [emailSettings]);

  // Save email settings mutation
  const saveEmailSettingsMutation = useMutation({
    mutationFn: async (settings: { stamp_mailbox: string }) => {
      if (emailSettings?.id) {
        const { data, error } = await supabase
          .from('email_settings')
          .update(settings)
          .eq('id', emailSettings.id)
          .select()
          .single();
        if (error) throw error;
        return data;
      } else {
        const { data, error } = await supabase
          .from('email_settings')
          .insert([{ ...settings, user_id: (await supabase.auth.getUser()).data.user?.id }])
          .select()
          .single();
        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      toast({
        title: "Settings Saved",
        description: "Email settings have been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['email-settings'] });
      setIsSettingsOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const { data: speakers = [], isLoading } = useQuery({
    queryKey: ['speakers'],
    queryFn: async () => {
      const { data, error } = await supabase.from('speakers').select('*');
      if (error) throw error;
      return (data || []) as Speaker[];
    }
  });

  const sendBirthdayEmailMutation = useMutation({
    mutationFn: async ({ speakerIds, template }: { speakerIds: string[], template: typeof emailTemplate }) => {
      const { data, error } = await supabase.functions.invoke('send-birthday-email', {
        body: { speakerIds, template }
      });
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      toast({
        title: "Birthday Emails Sent!",
        description: `Successfully processed ${data.sent || 0} birthday emails`,
      });
      setSelectedSpeakers([]);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to send birthday emails. Please try again.",
        variant: "destructive",
      });
    }
  });

  const getTodaysBirthdays = () => {
    return speakers.filter(speaker => {
      if (!speaker.date_of_birth) return false;
      const birthday = new Date(speaker.date_of_birth);
      const today = new Date();
      return birthday.getMonth() === today.getMonth() && birthday.getDate() === today.getDate();
    });
  };

  const getUpcomingBirthdays = () => {
    const today = new Date();
    const thisMonth = today.getMonth();
    
    return speakers
      .filter(speaker => {
        if (!speaker.date_of_birth) return false;
        const birthday = new Date(speaker.date_of_birth);
        return birthday.getMonth() === thisMonth && !isToday(new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate()));
      })
      .sort((a, b) => {
        const aBirthday = new Date(a.date_of_birth!);
        const bBirthday = new Date(b.date_of_birth!);
        return aBirthday.getDate() - bBirthday.getDate();
      });
  };

  const getThisMonthBirthdays = () => {
    const today = new Date();
    const thisMonth = today.getMonth();
    
    return speakers.filter(speaker => {
      if (!speaker.date_of_birth) return false;
      const birthday = new Date(speaker.date_of_birth);
      return birthday.getMonth() === thisMonth;
    });
  };

  const handleSendEmails = () => {
    if (selectedSpeakers.length === 0) {
      toast({
        title: "No speakers selected",
        description: "Please select at least one speaker to send birthday emails.",
        variant: "destructive",
      });
      return;
    }

    sendBirthdayEmailMutation.mutate({
      speakerIds: selectedSpeakers,
      template: emailTemplate
    });
  };

  const handleSaveTemplate = () => {
    setIsEditingTemplate(false);
    toast({
      title: "Template Saved",
      description: "Your birthday email template has been updated successfully.",
    });
  };

  const handleSaveSettings = () => {
    saveEmailSettingsMutation.mutate({ stamp_mailbox: stampMailbox });
  };

  const renderSpeakerCard = (speaker: Speaker, showCheckbox = false) => {
    const birthday = new Date(speaker.date_of_birth!);
    const today = new Date();
    const birthdayThisYear = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
    const isToday = birthday.getMonth() === today.getMonth() && birthday.getDate() === today.getDate();
    const isTomorrow = birthday.getMonth() === today.getMonth() && birthday.getDate() === today.getDate() + 1;

    return (
      <Card key={speaker.id} className="hover:shadow-md transition-all duration-200 border-l-4 border-l-pink-400">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              {showCheckbox && (
                <input
                  type="checkbox"
                  checked={selectedSpeakers.includes(speaker.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedSpeakers([...selectedSpeakers, speaker.id]);
                    } else {
                      setSelectedSpeakers(selectedSpeakers.filter(id => id !== speaker.id));
                    }
                  }}
                  className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
                />
              )}
              
              <Avatar className="h-14 w-14 border-2 border-pink-200">
                <AvatarFallback className="bg-gradient-to-br from-pink-100 to-purple-100 text-pink-700 font-semibold">
                  {speaker.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              
              <div className="space-y-2">
                <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">{speaker.name}</h3>
                <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4 text-pink-500" />
                    <span className="font-medium">{format(birthdayThisYear, "MMMM d")}</span>
                  </div>
                  {isToday && <Badge className="bg-pink-500 text-white">Today! 🎉</Badge>}
                  {isTomorrow && <Badge variant="secondary" className="bg-blue-100 text-blue-800">Tomorrow</Badge>}
                </div>
              </div>
            </div>
            
            <div className="text-center bg-gradient-to-br from-pink-50 to-purple-50 dark:from-pink-950/50 dark:to-purple-950/50 rounded-lg p-3">
              <Cake className="h-8 w-8 text-pink-500 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Age: {today.getFullYear() - birthday.getFullYear()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const todaysBirthdays = getTodaysBirthdays();
  const upcomingBirthdays = getUpcomingBirthdays();
  const thisMonthBirthdays = getThisMonthBirthdays();

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <Gift className="h-12 w-12 animate-pulse text-pink-500 mx-auto mb-4" />
            <p>Loading birthday information...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
              <Gift className="h-8 w-8 text-pink-500" />
              Birthday Management
            </h1>
            <p className="text-muted-foreground mt-2">
              Celebrate your speakers and send personalized birthday wishes
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Settings className="h-4 w-4" />
                  Email Settings
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Email Settings</DialogTitle>
                  <DialogDescription>
                    Configure your email settings for birthday notifications
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="stamp-mailbox">Stamp Mailbox</Label>
                    <Input
                      id="stamp-mailbox"
                      value={stampMailbox}
                      onChange={(e) => setStampMailbox(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      This email will appear as the sender for birthday emails
                    </p>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsSettingsOpen(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleSaveSettings}
                      disabled={saveEmailSettingsMutation.isPending}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog open={isEditingTemplate} onOpenChange={setIsEditingTemplate}>
              <DialogTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Edit3 className="h-4 w-4" />
                  Edit Email Template
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Edit Birthday Email Template</DialogTitle>
                  <DialogDescription>
                    Customize the birthday email template. Use {"{speaker_name}"} to personalize messages.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="subject">Email Subject</Label>
                    <Input
                      id="subject"
                      value={emailTemplate.subject}
                      onChange={(e) => setEmailTemplate({ ...emailTemplate, subject: e.target.value })}
                      placeholder="Email subject line"
                    />
                  </div>
                  <div>
                    <Label htmlFor="body">Email Body</Label>
                    <Textarea
                      id="body"
                      value={emailTemplate.body}
                      onChange={(e) => setEmailTemplate({ ...emailTemplate, body: e.target.value })}
                      placeholder="Email content"
                      rows={12}
                      className="resize-none"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsEditingTemplate(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSaveTemplate}>
                      Save Template
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Today's Birthdays Alert */}
        {todaysBirthdays.length > 0 && (
          <Card className="border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-950/20 dark:to-purple-950/20 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-pink-700 dark:text-pink-300">
                <Cake className="h-6 w-6" />
                🎉 Today's Birthdays ({todaysBirthdays.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {todaysBirthdays.map(speaker => renderSpeakerCard(speaker))}
              {todaysBirthdays.length > 0 && (
                <div className="pt-4 border-t">
                  <Button 
                    onClick={() => {
                      setSelectedSpeakers(todaysBirthdays.map(s => s.id));
                      handleSendEmails();
                    }}
                    className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white shadow-lg"
                    disabled={sendBirthdayEmailMutation.isPending}
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {sendBirthdayEmailMutation.isPending ? 'Sending...' : 'Send Birthday Wishes to All'}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* This Month's Birthdays */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-500" />
                This Month's Birthdays ({thisMonthBirthdays.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {thisMonthBirthdays.length > 0 ? (
                thisMonthBirthdays.map(speaker => renderSpeakerCard(speaker, true))
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <Gift className="h-16 w-16 mx-auto mb-4 opacity-30" />
                  <h3 className="text-lg font-medium mb-2">No birthdays this month</h3>
                  <p className="text-sm">Check back next month for celebrations!</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5 text-green-500" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary mb-1">
                  {selectedSpeakers.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  Selected for email
                </div>
              </div>

              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedSpeakers(thisMonthBirthdays.map(s => s.id))}
                  className="w-full"
                >
                  Select All This Month
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedSpeakers([])}
                  className="w-full"
                >
                  Clear All
                </Button>
              </div>

              <Separator />
              
              <Button 
                onClick={handleSendEmails}
                disabled={selectedSpeakers.length === 0 || sendBirthdayEmailMutation.isPending}
                className="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
              >
                <Send className="h-4 w-4 mr-2" />
                {sendBirthdayEmailMutation.isPending 
                  ? 'Sending...' 
                  : `Send Birthday Wishes (${selectedSpeakers.length})`
                }
              </Button>

              <div className="text-xs text-muted-foreground text-center pt-2">
                Emails will be sent using your configured stamp mailbox
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default BirthdayManagement;
