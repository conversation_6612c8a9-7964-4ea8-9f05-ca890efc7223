
import { useMemo } from 'react';
import { Speaker } from '../types/speaker';

export const useSpeakerStats = (speakers: Speaker[]) => {
  return useMemo(() => {
    if (!speakers || speakers.length === 0) {
      return {
        totalSpeakers: 0,
        busySpeakers: 0,
        unavailableSpeakers: 0,
        avgRate: 0,
        highestRate: 0,
        lowestRate: 0,
        categoryChartData: [],
      };
    }

    const totalSpeakers = speakers.length;
    const busySpeakers = speakers.filter((s) => s.availability === "Busy").length;
    const unavailableSpeakers = speakers.filter(
      (s) => s.availability === "Unavailable"
    ).length;

    const speakerRates = speakers.map((s) => s.rate).filter((r): r is number => r != null);
    
    const avgRate =
      speakerRates.length > 0
        ? Math.round(
            speakerRates.reduce((sum, rate) => sum + rate, 0) /
              speakerRates.length
          )
        : 0;
    const highestRate = speakerRates.length > 0 ? Math.max(...speakerRates) : 0;
    const lowestRate = speakerRates.length > 0 ? Math.min(...speakerRates) : 0;

    const categoryCount = speakers.reduce((acc, speaker) => {
      if (speaker.category) {
        acc[speaker.category] = (acc[speaker.category] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const categoryChartData = Object.entries(categoryCount).map(
      ([name, total]) => ({ name, total })
    );

    return {
      totalSpeakers,
      busySpeakers,
      unavailableSpeakers,
      avgRate,
      highestRate,
      lowestRate,
      categoryChartData,
    };
  }, [speakers]);
};
