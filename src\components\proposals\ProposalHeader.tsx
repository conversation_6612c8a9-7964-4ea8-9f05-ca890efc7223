
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit } from "lucide-react";

interface ProposalHeaderProps {
  isEditing: boolean;
  onBack: () => void;
  onEditTemplate: () => void;
}

export const ProposalHeader = ({ isEditing, onBack, onEditTemplate }: ProposalHeaderProps) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Proposals
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            {isEditing ? "Edit Proposal" : "Create Proposal"}
          </h1>
          <p className="text-muted-foreground mt-2">
            {isEditing
              ? "Update your speaker proposal"
              : "Create professional speaker proposals for your clients"}
          </p>
        </div>
      </div>
      <Button variant="outline" onClick={onEditTemplate}>
        <Edit className="h-4 w-4 mr-2" />
        Edit Template
      </Button>
    </div>
  );
};
