
import React from "react";
import { Proposal } from "../types/proposal";

interface ProposalPDFTemplateProps {
  proposal: Proposal;
}

export const ProposalPDFTemplate: React.FC<ProposalPDFTemplateProps> = ({
  proposal,
}) => {
  const totalBudget = proposal.speakers.reduce(
    (sum, { speaker }) => sum + (speaker.rate || 0),
    0
  );

  const theme = {
    colors: {
      primary: '#0D47A1',
      secondary: '#1976D2',
      accent: '#FFC107',
      textPrimary: '#212121',
      textSecondary: '#757575',
      background: '#FFFFFF',
      lightGray: '#F5F5F5',
    },
    fonts: {
      sans: 'Arial, Helvetica, sans-serif',
    }
  };

  const pageStyle: React.CSSProperties = {
    width: "210mm",
    height: "297mm",
    margin: 0,
    padding: '20mm',
    backgroundColor: theme.colors.background,
    boxSizing: "border-box",
    fontFamily: theme.fonts.sans,
    color: theme.colors.textPrimary,
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
  };

  const footerStyle: React.CSSProperties = {
    position: 'absolute',
    bottom: '10mm',
    left: '20mm',
    right: '20mm',
    fontSize: '10px',
    color: theme.colors.textSecondary,
    display: 'flex',
    justifyContent: 'space-between',
    borderTop: `1px solid #E0E0E0`,
    paddingTop: '5mm',
  };

  const headerStyle: React.CSSProperties = {
    borderBottom: `2px solid ${theme.colors.primary}`,
    paddingBottom: '10px',
    marginBottom: '20px',
  };

  const h1Style: React.CSSProperties = {
    fontSize: "36px",
    fontWeight: "bold",
    color: theme.colors.primary,
    margin: "0 0 10px 0",
  };

  const h2Style: React.CSSProperties = {
    fontSize: "24px",
    fontWeight: "bold",
    color: theme.colors.secondary,
    margin: "0 0 15px 0",
    borderBottom: `1px solid #E0E0E0`,
    paddingBottom: '8px',
  };

  // Calculate total pages
  const totalPages = 5 + proposal.speakers.length;
  let currentPage = 0;

  return (
    <div style={{ fontFamily: theme.fonts.sans, backgroundColor: 'white' }}>
      
      {/* PAGE 1: COVER PAGE */}
      <div className="pdf-page" style={{ ...pageStyle, justifyContent: 'center', alignItems: 'center', textAlign: 'center' }}>
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '8mm',
          height: '100%',
          backgroundColor: theme.colors.primary,
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          width: '50%',
          height: '8mm',
          backgroundColor: theme.colors.secondary,
        }}></div>

        <div style={{
          border: `2px solid ${theme.colors.primary}`,
          padding: '40px',
          width: '80%',
        }}>
          <p style={{
            fontSize: '18px',
            color: theme.colors.textSecondary,
            letterSpacing: '2px',
            textTransform: 'uppercase',
            marginBottom: '20px',
          }}>
            Speaker Proposal
          </p>
          <h1 style={{ ...h1Style, fontSize: '48px', margin: '0 0 20px 0' }}>
            {proposal.event.eventName}
          </h1>
          <p style={{ fontSize: '20px', marginBottom: '40px' }}>
            Prepared for: <span style={{ color: theme.colors.primary, fontWeight: 'bold' }}>{proposal.clientName}</span>
          </p>
          <p style={{ fontSize: '16px', color: theme.colors.textSecondary }}>
            {new Date(proposal.createdAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
          </p>
        </div>
        <div style={footerStyle}>
          <span>Speaker Agency | Professional Proposals</span>
          <span>Page {++currentPage} of {totalPages}</span>
        </div>
      </div>

      {/* PAGE 2: ABOUT US */}
      <div className="pdf-page" style={pageStyle}>
        <div style={headerStyle}>
          <h1 style={h1Style}>About Speaker Agency</h1>
        </div>
        <h2 style={h2Style}>Our Mission</h2>
        <p style={{ fontSize: "16px", color: theme.colors.textSecondary, lineHeight: 1.6, marginBottom: "30px" }}>
            At Speaker Agency, we connect world-class speakers with organizations seeking 
            exceptional thought leadership and inspiration. Our mission is to deliver 
            transformative experiences that educate, motivate, and drive meaningful change.
        </p>
        
        <div style={{ display: 'flex', gap: '20px', marginTop: 'auto' }}>
            <div style={{ flex: 1, backgroundColor: theme.colors.lightGray, padding: '20px', borderRadius: '4px', textAlign: 'center' }}>
                <div style={{ fontSize: "48px", fontWeight: "bold", color: theme.colors.primary, marginBottom: "8px" }}>500+</div>
                <div style={{ color: theme.colors.textSecondary }}>Expert Speakers</div>
            </div>
            <div style={{ flex: 1, backgroundColor: theme.colors.lightGray, padding: '20px', borderRadius: '4px', textAlign: 'center' }}>
                <div style={{ fontSize: "48px", fontWeight: "bold", color: theme.colors.primary, marginBottom: "8px" }}>1000+</div>
                <div style={{ color: theme.colors.textSecondary }}>Successful Events</div>
            </div>
            <div style={{ flex: 1, backgroundColor: theme.colors.lightGray, padding: '20px', borderRadius: '4px', textAlign: 'center' }}>
                <div style={{ fontSize: "48px", fontWeight: "bold", color: theme.colors.primary, marginBottom: "8px" }}>98%</div>
                <div style={{ color: theme.colors.textSecondary }}>Client Satisfaction</div>
            </div>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | About Us</span>
          <span>Page {++currentPage} of {totalPages}</span>
        </div>
      </div>

      {/* PAGE 3: EVENT DETAILS */}
      <div className="pdf-page" style={pageStyle}>
        <div style={headerStyle}>
          <h1 style={h1Style}>Event Overview</h1>
        </div>
        <h2 style={h2Style}>{proposal.event.eventName}</h2>
        <p style={{ fontSize: '16px', color: theme.colors.textSecondary, lineHeight: 1.6, marginBottom: "30px" }}>
          {proposal.event.description}
        </p>
        <div style={{ border: `1px solid #E0E0E0`, borderRadius: '4px', padding: '20px' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px 40px' }}>
            <div><strong>Date:</strong> {new Date(proposal.event.eventDate).toLocaleDateString()}</div>
            <div><strong>Location:</strong> {proposal.event.eventLocation}</div>
            <div><strong>Event Type:</strong> {proposal.event.eventType}</div>
            <div><strong>Audience Size:</strong> {proposal.event.audience}</div>
            <div><strong>Budget Range:</strong> {proposal.event.budget}</div>
          </div>
        </div>
        <div style={footerStyle}>
          <span>{proposal.event.eventName} | Event Overview</span>
          <span>Page {++currentPage} of {totalPages}</span>
        </div>
      </div>

      {/* PAGE 4+: SPEAKERS (One page per speaker) */}
      {proposal.speakers.map(({ speaker, role, notes }, index) => (
        <div key={speaker.id} className="pdf-page" style={pageStyle}>
          <div style={headerStyle}>
            <h1 style={h1Style}>Proposed Speaker</h1>
          </div>
          <div style={{ display: 'flex', gap: '20mm', flex: 1 }}>
            <div style={{ flexShrink: 0, width: '60mm', textAlign: 'center' }}>
              {speaker.image ? (
                <img
                  src={speaker.image}
                  alt={speaker.name}
                  style={{
                    width: "100%",
                    height: "auto",
                    aspectRatio: '1 / 1',
                    borderRadius: "50%",
                    objectFit: "cover",
                    marginBottom: '20px',
                    border: `4px solid ${theme.colors.primary}`
                  }}
                />
              ) : (
                <div
                  style={{
                    width: "100%",
                    aspectRatio: '1 / 1',
                    borderRadius: "50%",
                    backgroundColor: theme.colors.lightGray,
                    border: `4px solid ${theme.colors.primary}`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: '20px',
                    fontSize: '14px',
                    color: theme.colors.textSecondary
                  }}
                >
                  No Image
                </div>
              )}
              <h2 style={{ ...h2Style, borderBottom: 'none', margin: '0 0 5px 0', textAlign: 'center', fontSize: '22px' }}>{speaker.name}</h2>
              <p style={{ color: theme.colors.textSecondary, marginBottom: '20px' }}>{speaker.category || 'Speaker'}</p>
              <div style={{ backgroundColor: theme.colors.secondary, color: 'white', padding: '15px', borderRadius: '4px' }}>
                <div style={{ fontSize: "28px", fontWeight: "bold" }}>
                  ${(speaker.rate || 0).toLocaleString()}
                </div>
                <div style={{ fontSize: '12px', textTransform: 'uppercase' }}>Speaking Fee</div>
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <h2 style={{...h2Style, marginTop: 0}}>Biography</h2>
              <p style={{ fontSize: "14px", color: theme.colors.textSecondary, lineHeight: 1.6, marginBottom: "20px" }}>
                {speaker.bio || 'No biography available'}
              </p>
              <h2 style={h2Style}>Key Topics</h2>
              <ul style={{ paddingLeft: '20px', margin: '0 0 20px 0', columnCount: 2, columnGap: '20px' }}>
                {(speaker.specialties || ['General Speaking']).slice(0, 6).map((specialty, idx) => (
                  <li key={idx} style={{ marginBottom: '8px', color: theme.colors.textSecondary, fontSize: '14px' }}>{specialty}</li>
                ))}
              </ul>
              {notes && (
                <div style={{ marginTop: '20px', backgroundColor: '#FFF8E1', padding: '15px', borderRadius: '4px', borderLeft: `4px solid ${theme.colors.accent}` }}>
                  <h4 style={{ fontWeight: "bold", marginBottom: "8px", color: theme.colors.textPrimary, marginTop: 0 }}>Notes for this event:</h4>
                  <p style={{ color: theme.colors.textSecondary, fontSize: "14px", margin: 0 }}>{notes}</p>
                </div>
              )}
            </div>
          </div>
          <div style={footerStyle}>
            <span>{proposal.event.eventName} | Speaker Profile</span>
            <span>Page {++currentPage} of {totalPages}</span>
          </div>
        </div>
      ))}

      {/* PAGE: INVESTMENT */}
      <div className="pdf-page" style={pageStyle}>
        <div style={headerStyle}>
          <h1 style={h1Style}>Investment Summary</h1>
        </div>
        <h2 style={h2Style}>Speaker Fees</h2>
        <div style={{ border: '1px solid #E0E0E0', borderRadius: '4px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '10px 20px', backgroundColor: theme.colors.lightGray, fontWeight: 'bold' }}>
            <span>Speaker</span>
            <span>Fee</span>
          </div>
          {proposal.speakers.map(({ speaker }, index) => (
            <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '15px 20px', borderTop: '1px solid #E0E0E0' }}>
              <div>
                <span style={{ fontWeight: "500" }}>{speaker.name}</span>
                <br />
                <span style={{ fontSize: "12px", color: theme.colors.textSecondary }}>{speaker.category || 'Speaker'}</span>
              </div>
              <span style={{ fontWeight: "bold", color: theme.colors.primary, fontSize: "18px" }}>
                ${(speaker.rate || 0).toLocaleString()}
              </span>
            </div>
          ))}
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '20px', borderTop: `2px solid ${theme.colors.primary}`, backgroundColor: theme.colors.lightGray }}>
            <span style={{ fontWeight: "bold", fontSize: "20px" }}>Total Investment</span>
            <span style={{ fontWeight: "bold", color: theme.colors.primary, fontSize: "24px" }}>
              ${totalBudget.toLocaleString()}
            </span>
          </div>
        </div>
        <div style={{ marginTop: '30px' }}>
          <h2 style={h2Style}>Terms & Conditions</h2>
          <p style={{ fontSize: "12px", color: theme.colors.textSecondary, lineHeight: 1.6 }}>
            - Fees are inclusive of all preparation and presentation time. Travel and accommodation expenses are additional unless otherwise specified.
            <br />
            - A 50% deposit is required upon confirmation to secure the speaker(s).
            <br />
            - The remaining balance is due 14 days prior to the event date.
            <br />
            - This proposal is valid for 30 days.
          </p>
        </div>
        <div style={footerStyle}>
          <span>{proposal.event.eventName} | Investment</span>
          <span>Page {++currentPage} of {totalPages}</span>
        </div>
      </div>

      {/* PAGE: THANK YOU & CONTACT */}
      <div className="pdf-page" style={{...pageStyle, textAlign: 'center', justifyContent: 'center' }}>
        <h1 style={{...h1Style, fontSize: '42px'}}>Thank You</h1>
        <p style={{ fontSize: "18px", color: theme.colors.textSecondary, maxWidth: '500px', margin: '0 auto 40px auto', lineHeight: 1.6 }}>
          We are excited about the possibility of partnering with you for {proposal.event.eventName}. Please let us know if you have any questions.
        </p>

        <div style={{ borderTop: `1px solid #E0E0E0`, paddingTop: '30px', marginTop: '30px', display: 'inline-block', textAlign: 'left' }}>
          <h2 style={{...h2Style, border: 'none', margin: '0 0 20px 0', textAlign: 'center' }}>Next Steps</h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
            <div><strong>1. Review:</strong> Discuss this proposal with your team.</div>
            <div><strong>2. Connect:</strong> Schedule a call with us to finalize details.</div>
            <div><strong>3. Confirm:</strong> Sign the agreement and we'll secure your speakers!</div>
          </div>
        </div>
        
        <div style={{ marginTop: 'auto', width: '100%', textAlign: 'center' }}>
          <p style={{ fontWeight: 'bold', color: theme.colors.primary }}>Speaker Agency</p>
          <p style={{ color: theme.colors.textSecondary, fontSize: '12px' }}>
            <EMAIL> | (555) 123-4567 | www.speakeragency.com
          </p>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Contact</span>
          <span>Page {++currentPage} of {totalPages}</span>
        </div>
      </div>
    </div>
  );
};
