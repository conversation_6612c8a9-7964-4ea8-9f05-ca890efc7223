
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface SpeakerCategoryChartProps {
  data: { name: string; total: number }[];
}

const SpeakerCategoryChart = ({ data }: SpeakerCategoryChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Speaker Categories</CardTitle>
      </CardHeader>
      <CardContent>
        {data.length > 0 ? (
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={data}>
              <XAxis
                dataKey="name"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `${value}`}
                allowDecimals={false}
              />
              <Tooltip
                cursor={{ fill: "hsl(var(--muted))" }}
                contentStyle={{ backgroundColor: "hsl(var(--background))", border: "1px solid hsl(var(--border))" }}
              />
              <Bar dataKey="total" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            No category data to display.
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SpeakerCategoryChart;
