
import React from 'react';
import { Proposal, ProposalSpeaker, ProposalTemplateSettings } from '../types/proposal';

interface CustomProposalPDFTemplateProps {
  proposal: Proposal;
  templateSettings: ProposalTemplateSettings | null | undefined;
}

export const CustomProposalPDFTemplate: React.FC<CustomProposalPDFTemplateProps> = ({ proposal, templateSettings }) => {
  const settings = {
    cover_page_title: templateSettings?.cover_page_title || 'Speaker Proposal',
    cover_page_image_url: templateSettings?.cover_page_image_url || 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=1200&h=800&fit=crop',
    about_us_mission: templateSettings?.about_us_mission || 'At Speaker Agency, we connect world-class speakers with organizations seeking exceptional thought leadership and inspiration. Our mission is to deliver transformative experiences that educate, motivate, and drive meaningful change.',
  };

  const styles: { [key: string]: React.CSSProperties } = {
    page: {
      width: '210mm',
      minHeight: '297mm',
      padding: '20mm',
      margin: '0 auto',
      boxSizing: 'border-box',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: 'white',
      fontFamily: 'sans-serif',
      color: '#333',
    },
    coverPage: {
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      backgroundImage: `linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url(${settings.cover_page_image_url})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      color: 'white',
    },
    h1: { fontSize: '48px', margin: '0 0 20px 0' },
    h2: { fontSize: '32px', margin: '40px 0 20px 0', borderBottom: '2px solid #333', paddingBottom: '10px' },
    h3: { fontSize: '24px', margin: '20px 0 10px 0' },
    p: { fontSize: '16px', lineHeight: '1.6' },
    speakerCard: { border: '1px solid #eee', padding: '15px', borderRadius: '8px', marginBottom: '20px', display: 'flex', gap: '20px' },
    speakerImage: { width: '100px', height: '100px', borderRadius: '50%', objectFit: 'cover' },
    total: { marginTop: 'auto', paddingTop: '20px', borderTop: '2px solid #333', textAlign: 'right', fontSize: '28px', fontWeight: 'bold' }
  };

  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <title>{`Proposal for ${proposal.clientName}`}</title>
      </head>
      <body>
        <div className="pdf-page" style={{...styles.page, ...styles.coverPage}}>
          <h1 style={styles.h1}>{settings.cover_page_title}</h1>
          <p style={styles.p}>Prepared for: {proposal.clientName}</p>
          <p style={styles.p}>Event: {proposal.event.eventName}</p>
          <p style={styles.p}>Date: {new Date(proposal.createdAt).toLocaleDateString()}</p>
        </div>

        <div className="pdf-page" style={styles.page}>
          <h2 style={styles.h2}>Our Mission</h2>
          <p style={styles.p}>{settings.about_us_mission}</p>
          <h2 style={styles.h2}>Event Details</h2>
          <p style={styles.p}><strong>Event:</strong> {proposal.event.eventName}</p>
          <p style={styles.p}><strong>Date:</strong> {proposal.event.eventDate}</p>
          <p style={styles.p}><strong>Location:</strong> {proposal.event.eventLocation}</p>
          <p style={styles.p}><strong>Audience:</strong> {proposal.event.audience}</p>
        </div>

        {proposal.speakers.map((speaker) => (
          <div key={speaker.speaker.id} className="pdf-page" style={styles.page}>
            <h2 style={styles.h2}>Proposed Speaker</h2>
            <div style={styles.speakerCard}>
              <img src={speaker.speaker.image} alt={speaker.speaker.name} style={styles.speakerImage} />
              <div>
                <h3 style={styles.h3}>{speaker.speaker.name}</h3>
                <p style={styles.p}><strong>Role:</strong> {speaker.role}</p>
                <p style={styles.p}><strong>Bio:</strong> {speaker.speaker.bio}</p>
              </div>
            </div>
            {speaker.notes && <p style={styles.p}><strong>Notes:</strong> {speaker.notes}</p>}
          </div>
        ))}
        
        <div className="pdf-page" style={styles.page}>
           <h2 style={styles.h2}>Investment Summary</h2>
            {proposal.speakers.map(({ speaker }) => (
              <div key={speaker.id} style={{ display: 'flex', justifyContent: 'space-between', fontSize: '18px', padding: '10px 0', borderBottom: '1px solid #eee' }}>
                <span>{speaker.name} - {speaker.category}</span>
                <span>${speaker.rate.toLocaleString()}</span>
              </div>
            ))}
            <div style={styles.total}>
              <span>Total: ${proposal.totalBudget.toLocaleString()}</span>
            </div>
        </div>

      </body>
    </html>
  );
};
