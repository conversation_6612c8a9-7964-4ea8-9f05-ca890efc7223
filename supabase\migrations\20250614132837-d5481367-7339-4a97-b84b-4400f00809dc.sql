
-- Create a new storage bucket for proposals
INSERT INTO storage.buckets (id, name, public)
VALUES ('proposals', 'proposals', true);

-- Set up RLS policies for the proposals bucket
-- Allow public read access to all files in the 'proposals' bucket
CREATE POLICY "Public read access for proposals"
ON storage.objects FOR SELECT
USING ( bucket_id = 'proposals' );

-- Allow authenticated users to upload, update, and delete files
CREATE POLICY "Authenticated users can manage proposal files"
ON storage.objects FOR ALL
USING ( auth.role() = 'authenticated' AND bucket_id = 'proposals' );

-- Add a column to proposals table to store the PDF URL
ALTER TABLE public.proposals ADD COLUMN pdf_url text;
