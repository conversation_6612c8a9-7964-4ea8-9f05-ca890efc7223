
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "../components/Layout";
import SpeakerCard from "../components/SpeakerCard";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Search, Filter, UserPlus, X, Loader2, Settings } from "lucide-react";
import { toast } from "sonner";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Speaker } from "@/types/speaker";
import { Skeleton } from "@/components/ui/skeleton";
import ManageTags from "../components/ManageTags";

type NewSpeaker = Omit<Speaker, 'id' | 'created_at'>;

const Speakers = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedAvailability, setSelectedAvailability] = useState("all");
  const [minRate, setMinRate] = useState("");
  const [maxRate, setMaxRate] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const SPEAKERS_PER_PAGE = 8;
  
  const [isCreateSpeakerOpen, setIsCreateSpeakerOpen] = useState(false);
  const [isManageCategoriesOpen, setIsManageCategoriesOpen] = useState(false);
  const [isManageSpecialtiesOpen, setIsManageSpecialtiesOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newSpeaker, setNewSpeaker] = useState<Partial<NewSpeaker>>({
    name: "",
    bio: "",
    category: "",
    location: "",
    experience: "",
    rate: null,
    specialties: [],
    availability: "Available",
    date_of_birth: null
  });

  const { data: speakers = [], isLoading: isLoadingSpeakers } = useQuery<Speaker[]>({
    queryKey: ['speakers'],
    queryFn: async () => {
      const { data, error } = await supabase.from('speakers').select('*').order('name');
      if (error) throw new Error(error.message);
      return (data || []) as Speaker[];
    }
  });

  const { data: categories = [] } = useQuery<{id: string, name: string}[]>({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase.from('categories').select('id, name').order('name');
      if (error) throw new Error(error.message);
      return data || [];
    },
  });

  const { data: allSpecialties = [] } = useQuery<{id: string, name: string}[]>({
    queryKey: ['allSpecialties'],
    queryFn: async () => {
      const { data, error } = await supabase.from('specialties').select('id, name').order('name');
      if (error) throw new Error(error.message);
      return data || [];
    },
  });
  
  const availabilityOptions = ['Available', 'Busy', 'Unavailable'];

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedCategory, selectedAvailability, minRate, maxRate]);

  const filteredSpeakers = speakers.filter(speaker => {
    const matchesSearch = searchTerm === "" || 
                         speaker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (speaker.bio && speaker.bio.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (speaker.specialties && speaker.specialties.some(specialty => 
                           specialty.toLowerCase().includes(searchTerm.toLowerCase())
                         )) ||
                         (speaker.location && speaker.location.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || speaker.category === selectedCategory;
    const matchesAvailability = selectedAvailability === "all" || speaker.availability === selectedAvailability;
    
    const matchesMinRate = minRate === "" || (speaker.rate && speaker.rate >= parseInt(minRate));
    const matchesMaxRate = maxRate === "" || (speaker.rate && speaker.rate <= parseInt(maxRate));
    
    return matchesSearch && matchesCategory && matchesAvailability && matchesMinRate && matchesMaxRate;
  });

  const totalPages = Math.ceil(filteredSpeakers.length / SPEAKERS_PER_PAGE);
  const paginatedSpeakers = filteredSpeakers.slice(
    (currentPage - 1) * SPEAKERS_PER_PAGE,
    currentPage * SPEAKERS_PER_PAGE
  );

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  const getPages = () => {
    const pages = [];
    if (totalPages <= 5) {
        for (let i = 1; i <= totalPages; i++) {
            pages.push(i);
        }
    } else {
        if (currentPage <= 3) {
            pages.push(1, 2, 3, 4, '...', totalPages);
        } else if (currentPage >= totalPages - 2) {
            pages.push(1, '...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
        } else {
            pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);
        }
    }
    return pages;
  };

  const handleSpeakerClick = (speakerId: string) =>
    navigate(`/speaker/${speakerId}`);

  const createSpeakerMutation = useMutation({
    mutationFn: async (speakerData: NewSpeaker) => {
      const { data, error } = await supabase.from('speakers').insert([speakerData]).select();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success("Speaker created successfully!");
      queryClient.invalidateQueries({ queryKey: ['speakers'] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      queryClient.invalidateQueries({ queryKey: ['allSpecialties'] });
      setIsCreateSpeakerOpen(false);
      setNewSpeaker({
        name: "", bio: "", category: "", location: "", experience: "", rate: null, specialties: [], availability: "Available", date_of_birth: null
      });
    },
    onError: (error) => {
      toast.error(`Error creating speaker: ${error.message}`);
    }
  });

  const handleCreateSpeaker = async () => {
    if (!newSpeaker.name || !newSpeaker.bio || !newSpeaker.category) {
      toast.error("Please fill in all required fields");
      return;
    }
    setIsCreating(true);

    try {
      const speakerToCreate: NewSpeaker = {
        name: newSpeaker.name,
        bio: newSpeaker.bio,
        category: newSpeaker.category,
        availability: newSpeaker.availability || 'Available',
        rate: newSpeaker.rate ? Number(newSpeaker.rate) : null,
        location: newSpeaker.location || null,
        experience: newSpeaker.experience || null,
        specialties: newSpeaker.specialties || [],
        date_of_birth: newSpeaker.date_of_birth || null,
      };

      await createSpeakerMutation.mutateAsync(speakerToCreate);
      
    } catch (error: any) {
      toast.error(`Error creating speaker: ${error.message}`);
    } finally {
      setIsCreating(false);
    }
  };
  
  const clearFilters = () => {
    setSearchTerm("");
    setSelectedCategory("all");
    setSelectedAvailability("all");
    setMinRate("");
    setMaxRate("");
  };

  const toggleSpecialty = (specialty: string) => {
    setNewSpeaker(prev => ({
      ...prev,
      specialties: prev.specialties?.includes(specialty)
        ? prev.specialties.filter(s => s !== specialty)
        : [...(prev.specialties || []), specialty]
    }));
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Speakers Database</h1>
            <p className="text-muted-foreground mt-2">
              Browse and manage our collection of professional speakers
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => setIsManageCategoriesOpen(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Manage Categories
            </Button>
            <Button variant="outline" onClick={() => setIsManageSpecialtiesOpen(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Manage Specialties
            </Button>
            <Dialog open={isCreateSpeakerOpen} onOpenChange={setIsCreateSpeakerOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Speaker
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create New Speaker</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="speaker-name">Name *</Label>
                      <Input
                        id="speaker-name"
                        value={newSpeaker.name}
                        onChange={(e) => setNewSpeaker(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Speaker name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="speaker-category">Category *</Label>
                      <Select value={newSpeaker.category} onValueChange={(value) => setNewSpeaker(prev => ({ ...prev, category: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map(category => (
                            <SelectItem key={category.id} value={category.name}>{category.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="speaker-bio">Bio *</Label>
                    <Textarea
                      id="speaker-bio"
                      value={newSpeaker.bio}
                      onChange={(e) => setNewSpeaker(prev => ({ ...prev, bio: e.target.value }))}
                      placeholder="Speaker biography"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="speaker-availability">Availability</Label>
                      <Select value={newSpeaker.availability || ''} onValueChange={(value: any) => setNewSpeaker(prev => ({ ...prev, availability: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Available">Available</SelectItem>
                          <SelectItem value="Busy">Busy</SelectItem>
                          <SelectItem value="Unavailable">Unavailable</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="speaker-location">Location</Label>
                      <Input
                        id="speaker-location"
                        value={newSpeaker.location || ''}
                        onChange={(e) => setNewSpeaker(prev => ({ ...prev, location: e.target.value }))}
                        placeholder="City, State"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="speaker-experience">Experience</Label>
                      <Input
                        id="speaker-experience"
                        value={newSpeaker.experience || ''}
                        onChange={(e) => setNewSpeaker(prev => ({ ...prev, experience: e.target.value }))}
                        placeholder="15+ years in..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="speaker-rate">Rate ($)</Label>
                      <Input
                        id="speaker-rate"
                        type="number"
                        value={newSpeaker.rate || ''}
                        onChange={(e) => setNewSpeaker(prev => ({ ...prev, rate: Number(e.target.value) }))}
                        placeholder="25000"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="speaker-date-of-birth">Date of Birth</Label>
                    <Input
                      id="speaker-date-of-birth"
                      type="date"
                      value={newSpeaker.date_of_birth || ''}
                      onChange={(e) => setNewSpeaker(prev => ({ ...prev, date_of_birth: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label>Specialties</Label>
                    <div className="border rounded-md p-2 grid grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto">
                      {allSpecialties.map(specialty => (
                        <label key={specialty.id} className="flex items-center space-x-2 text-sm font-normal">
                          <input
                            type="checkbox"
                            checked={newSpeaker.specialties?.includes(specialty.name)}
                            onChange={() => toggleSpecialty(specialty.name)}
                            className="rounded"
                          />
                          <span>{specialty.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <Button onClick={handleCreateSpeaker} disabled={isCreating} className="w-full">
                    {isCreating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    {isCreating ? 'Creating Speaker...' : 'Create Speaker'}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <ManageTags type="Category" open={isManageCategoriesOpen} onOpenChange={setIsManageCategoriesOpen} />
        <ManageTags type="Specialty" open={isManageSpecialtiesOpen} onOpenChange={setIsManageSpecialtiesOpen} />

        {/* Search and Filters */}
        <div className="bg-card p-6 rounded-lg border border-border space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium text-foreground">Search & Filter</span>
            </div>
            <Button variant="outline" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
          
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search by name, bio, specialties, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Category</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category.id} value={category.name}>{category.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Availability</Label>
                <Select value={selectedAvailability} onValueChange={setSelectedAvailability}>
                  <SelectTrigger>
                    <SelectValue placeholder="Availability" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Availability</SelectItem>
                    {availabilityOptions.map(availability => (
                      <SelectItem key={availability} value={availability}>{availability}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Min Rate ($)</Label>
                <Input
                  type="number"
                  placeholder="0"
                  value={minRate}
                  onChange={(e) => setMinRate(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Max Rate ($)</Label>
                <Input
                  type="number"
                  placeholder="50000"
                  value={maxRate}
                  onChange={(e) => setMaxRate(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* Active Filters */}
        {(searchTerm || selectedCategory !== "all" || selectedAvailability !== "all" || minRate || maxRate) && (
          <div className="flex items-center space-x-2 flex-wrap gap-y-2">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {searchTerm && (
              <Badge variant="outline">
                Search: "{searchTerm}"
              </Badge>
            )}
            {selectedCategory !== "all" && (
              <Badge variant="outline">
                Category: {selectedCategory}
              </Badge>
            )}
            {selectedAvailability !== "all" && (
              <Badge variant="outline">
                Availability: {selectedAvailability}
              </Badge>
            )}
            {minRate && (
              <Badge variant="outline">
                Min Rate: ${parseInt(minRate).toLocaleString()}
              </Badge>
            )}
            {maxRate && (
              <Badge variant="outline">
                Max Rate: ${parseInt(maxRate).toLocaleString()}
              </Badge>
            )}
          </div>
        )}

        {/* Results Summary */}
        <div className="flex items-center justify-between">
          <span className="text-muted-foreground">
            {isLoadingSpeakers 
              ? "Loading speakers..." 
              : `Showing ${paginatedSpeakers.length} of ${filteredSpeakers.length} speakers`
            }
          </span>
        </div>

        {/* Speakers Grid */}
        {isLoadingSpeakers ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="space-y-4">
                <Skeleton className="h-24 w-24 rounded-full mx-auto" />
                <Skeleton className="h-4 w-3/4 mx-auto" />
                <Skeleton className="h-4 w-1/2 mx-auto" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {paginatedSpeakers.map(speaker => (
              <SpeakerCard
                key={speaker.id}
                speaker={speaker}
                onClick={() => handleSpeakerClick(speaker.id)}
              />
            ))}
          </div>
        )}

        {filteredSpeakers.length === 0 && !isLoadingSpeakers && (
          <div className="text-center py-12 col-span-full">
            <div className="text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg">No speakers found</p>
              <p className="text-sm">Try adjusting your search criteria</p>
            </div>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(currentPage - 1);
                  }}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : undefined}
                />
              </PaginationItem>
              
              {getPages().map((page, index) => (
                <PaginationItem key={`${page}-${index}`}>
                  {typeof page === 'number' ? (
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(page);
                      }}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  ) : (
                    <PaginationEllipsis />
                  )}
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(currentPage + 1);
                  }}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : undefined}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    </Layout>
  );
};

export default Speakers;
