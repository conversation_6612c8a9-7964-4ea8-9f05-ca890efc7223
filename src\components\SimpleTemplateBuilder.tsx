import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Loader2, Plus, Eye, Save, Download, Palette, Type, Layout, Image as ImageIcon } from 'lucide-react';

const templateSchema = z.object({
  cover_page_title: z.string().min(1, 'Cover title is required'),
  cover_page_image_url: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  about_us_mission: z.string().min(1, 'About us text is required'),
  
  // Colors
  primary_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color').optional(),
  secondary_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color').optional(),
  accent_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color').optional(),
  
  // Typography
  heading_font: z.string().optional(),
  body_font: z.string().optional(),
  font_size: z.number().min(8).max(24).optional(),
  
  // Layout Options
  layout_style: z.enum(['classic', 'modern', 'minimal']).optional(),
  show_speaker_images: z.boolean().optional(),
  show_speaker_rates: z.boolean().optional(),
  watermark_text: z.string().optional(),
});

type TemplateFormValues = z.infer<typeof templateSchema>;

interface SimpleTemplateBuilderProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const fontOptions = [
  { value: 'Arial, sans-serif', label: 'Arial' },
  { value: 'Helvetica, sans-serif', label: 'Helvetica' },
  { value: 'Georgia, serif', label: 'Georgia' },
  { value: 'Times New Roman, serif', label: 'Times New Roman' },
  { value: 'Roboto, sans-serif', label: 'Roboto' },
  { value: 'Open Sans, sans-serif', label: 'Open Sans' },
  { value: 'Montserrat, sans-serif', label: 'Montserrat' },
  { value: 'Playfair Display, serif', label: 'Playfair Display' },
];

export function SimpleTemplateBuilder({ open, onOpenChange }: SimpleTemplateBuilderProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  const { data: settings, isLoading } = useQuery({
    queryKey: ['proposalTemplateSettings'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data, error } = await supabase
        .from('proposal_template_settings')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) throw new Error(error.message);
      return data;
    },
  });

  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      cover_page_title: 'Speaker Proposal',
      cover_page_image_url: '',
      about_us_mission: 'We are dedicated to providing exceptional speaking services...',
      primary_color: '#3B82F6',
      secondary_color: '#1E40AF',
      accent_color: '#F59E0B',
      heading_font: 'Montserrat, sans-serif',
      body_font: 'Open Sans, sans-serif',
      font_size: 12,
      layout_style: 'modern',
      show_speaker_images: true,
      show_speaker_rates: true,
      watermark_text: '',
    },
  });

  useEffect(() => {
    if (settings) {
      form.reset({
        cover_page_title: settings.cover_page_title || 'Speaker Proposal',
        cover_page_image_url: settings.cover_page_image_url || '',
        about_us_mission: settings.about_us_mission || 'We are dedicated to providing exceptional speaking services...',
        primary_color: '#3B82F6',
        secondary_color: '#1E40AF', 
        accent_color: '#F59E0B',
        heading_font: 'Montserrat, sans-serif',
        body_font: 'Open Sans, sans-serif',
        font_size: 12,
        layout_style: 'modern',
        show_speaker_images: true,
        show_speaker_rates: true,
        watermark_text: '',
      });
    }
  }, [settings, form]);

  const saveTemplateMutation = useMutation({
    mutationFn: async (values: TemplateFormValues) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('You must be logged in to save templates.');

      const { data, error } = await supabase.from('proposal_template_settings').upsert({
        user_id: user.id,
        id: settings?.id,
        cover_page_title: values.cover_page_title,
        cover_page_image_url: values.cover_page_image_url,
        about_us_mission: values.about_us_mission,
        updated_at: new Date().toISOString(),
      }, { onConflict: 'user_id' }).select().single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['proposalTemplateSettings'] });
      toast({ title: 'Template saved successfully!' });
      onOpenChange(false);
    },
    onError: (error) => {
      toast({ title: 'Error saving template', description: error.message, variant: 'destructive' });
    },
  });

  const onSubmit = (values: TemplateFormValues) => {
    saveTemplateMutation.mutate(values);
  };

  const watchedValues = form.watch();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Customize Proposal Template
          </DialogTitle>
          <DialogDescription>
            Design your proposal template with custom layouts, colors, fonts, and content options.
          </DialogDescription>
        </DialogHeader>

        <div className="flex h-[70vh]">
          {/* Left Panel - Form */}
          <div className="w-2/3 pr-4 overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center h-40">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="general">General</TabsTrigger>
                      <TabsTrigger value="colors">Colors</TabsTrigger>
                      <TabsTrigger value="typography">Typography</TabsTrigger>
                      <TabsTrigger value="layout">Layout</TabsTrigger>
                    </TabsList>

                    <TabsContent value="general" className="space-y-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Template Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="cover_page_title"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Cover Page Title</FormLabel>
                                <FormControl><Input placeholder="Speaker Proposal" {...field} /></FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="cover_page_image_url"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Cover Page Background Image</FormLabel>
                                {(field.value && !isUploading) && (
                                  <div className="mt-2">
                                    <img src={field.value} alt="Cover preview" className="rounded-md max-h-40 w-full object-cover" />
                                  </div>
                                )}
                                {isUploading && (
                                  <div className="flex items-center justify-center h-40 border rounded-md">
                                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                                  </div>
                                )}
                                <FormControl>
                                  <Input
                                    type="file"
                                    accept="image/*"
                                    className="mt-2"
                                    disabled={isUploading || saveTemplateMutation.isPending}
                                    onChange={async (e) => {
                                      const file = e.target.files?.[0];
                                      if (!file) return;

                                      setIsUploading(true);
                                      const { data: { user } } = await supabase.auth.getUser();
                                      if (!user) {
                                        toast({ title: 'Authentication Error', description: 'You must be logged in.', variant: 'destructive' });
                                        setIsUploading(false);
                                        return;
                                      }

                                      const filePath = `${user.id}/${Date.now()}_${file.name}`;
                                      const { error: uploadError } = await supabase.storage
                                        .from('template_images')
                                        .upload(filePath, file);
                                      
                                      if (uploadError) {
                                        toast({ title: 'Upload failed', description: uploadError.message, variant: 'destructive' });
                                        setIsUploading(false);
                                        return;
                                      }

                                      const { data: { publicUrl } } = supabase.storage
                                        .from('template_images')
                                        .getPublicUrl(filePath);
                                      
                                      field.onChange(publicUrl);
                                      setIsUploading(false);
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="about_us_mission"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>About Us / Mission Statement</FormLabel>
                                <FormControl><Textarea rows={4} placeholder="Our mission is to..." {...field} /></FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="colors" className="space-y-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Color Scheme</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 gap-4">
                            <FormField
                              control={form.control}
                              name="primary_color"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Primary Color</FormLabel>
                                  <div className="flex gap-2">
                                    <FormControl>
                                      <Input type="color" {...field} className="w-16 h-10 p-1" />
                                    </FormControl>
                                    <FormControl>
                                      <Input {...field} placeholder="#3B82F6" />
                                    </FormControl>
                                  </div>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="secondary_color"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Secondary Color</FormLabel>
                                  <div className="flex gap-2">
                                    <FormControl>
                                      <Input type="color" {...field} className="w-16 h-10 p-1" />
                                    </FormControl>
                                    <FormControl>
                                      <Input {...field} placeholder="#1E40AF" />
                                    </FormControl>
                                  </div>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="accent_color"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Accent Color</FormLabel>
                                  <div className="flex gap-2">
                                    <FormControl>
                                      <Input type="color" {...field} className="w-16 h-10 p-1" />
                                    </FormControl>
                                    <FormControl>
                                      <Input {...field} placeholder="#F59E0B" />
                                    </FormControl>
                                  </div>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="typography" className="space-y-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Typography Settings</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 gap-4">
                            <FormField
                              control={form.control}
                              name="heading_font"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Heading Font</FormLabel>
                                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select heading font" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {fontOptions.map((font) => (
                                        <SelectItem key={font.value} value={font.value}>
                                          {font.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="body_font"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Body Font</FormLabel>
                                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select body font" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {fontOptions.map((font) => (
                                        <SelectItem key={font.value} value={font.value}>
                                          {font.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="font_size"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Base Font Size: {field.value}px</FormLabel>
                                  <FormControl>
                                    <Slider
                                      min={8}
                                      max={24}
                                      step={1}
                                      value={[field.value || 12]}
                                      onValueChange={(value) => field.onChange(value[0])}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="layout" className="space-y-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Layout Options</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="layout_style"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Layout Style</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="classic">Classic</SelectItem>
                                    <SelectItem value="modern">Modern</SelectItem>
                                    <SelectItem value="minimal">Minimal</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <Separator />

                          <div className="space-y-3">
                            <Label className="text-base font-medium">Content Options</Label>
                            <div className="grid grid-cols-1 gap-3">
                              <FormField
                                control={form.control}
                                name="show_speaker_images"
                                render={({ field }) => (
                                  <FormItem className="flex items-center space-x-2">
                                    <FormControl>
                                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                                    </FormControl>
                                    <FormLabel>Show Speaker Images</FormLabel>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="show_speaker_rates"
                                render={({ field }) => (
                                  <FormItem className="flex items-center space-x-2">
                                    <FormControl>
                                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                                    </FormControl>
                                    <FormLabel>Show Speaker Rates</FormLabel>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          <FormField
                            control={form.control}
                            name="watermark_text"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Watermark Text (Optional)</FormLabel>
                                <FormControl>
                                  <Input placeholder="CONFIDENTIAL" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>

                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={saveTemplateMutation.isPending || isUploading}>
                      {saveTemplateMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      <Save className="mr-2 h-4 w-4" />
                      Save Template
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            )}
          </div>

          {/* Right Panel - Preview */}
          <div className="w-1/3 pl-4 border-l">
            <div className="sticky top-0">
              <div className="flex items-center gap-2 mb-4">
                <Eye className="h-4 w-4" />
                <span className="font-medium">Live Preview</span>
              </div>
              
              <div 
                className="border rounded-lg p-4 bg-white shadow-sm min-h-[500px] relative"
                style={{
                  color: '#1F2937',
                  fontFamily: watchedValues.body_font || 'Open Sans, sans-serif',
                  fontSize: `${watchedValues.font_size || 12}px`,
                }}
              >
                {/* Preview Cover */}
                <div 
                  className="text-center p-6 mb-4 rounded"
                  style={{ backgroundColor: watchedValues.primary_color || '#3B82F6', color: 'white' }}
                >
                  <h1 
                    className="text-2xl font-bold mb-2"
                    style={{ fontFamily: watchedValues.heading_font || 'Montserrat, sans-serif' }}
                  >
                    {watchedValues.cover_page_title}
                  </h1>
                </div>

                {/* Preview Content */}
                <div className="space-y-4">
                  <div>
                    <h2 
                      className="text-lg font-semibold mb-2"
                      style={{ 
                        fontFamily: watchedValues.heading_font || 'Montserrat, sans-serif',
                        color: watchedValues.secondary_color || '#1E40AF'
                      }}
                    >
                      About Us
                    </h2>
                    <p className="text-sm">{watchedValues.about_us_mission}</p>
                  </div>

                  <div>
                    <h2 
                      className="text-lg font-semibold mb-2"
                      style={{ 
                        fontFamily: watchedValues.heading_font || 'Montserrat, sans-serif',
                        color: watchedValues.secondary_color || '#1E40AF'
                      }}
                    >
                      Speaker Profile
                    </h2>
                    <div className="flex items-center gap-3">
                      {watchedValues.show_speaker_images && (
                        <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center text-xs">
                          IMG
                        </div>
                      )}
                      <div className="flex-1">
                        <h3 className="font-medium">Dr. Jane Smith</h3>
                        <p className="text-xs text-gray-600">Expert in technology and innovation...</p>
                        {watchedValues.show_speaker_rates && (
                          <span 
                            className="text-sm font-medium"
                            style={{ color: watchedValues.accent_color || '#F59E0B' }}
                          >
                            $25,000
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {watchedValues.watermark_text && (
                  <div 
                    className="absolute inset-0 flex items-center justify-center pointer-events-none"
                    style={{ 
                      transform: 'rotate(-45deg)',
                      opacity: 0.1,
                      fontSize: '24px',
                      fontWeight: 'bold'
                    }}
                  >
                    {watchedValues.watermark_text}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}