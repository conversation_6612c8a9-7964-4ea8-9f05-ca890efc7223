
import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, MapPin, Users, ArrowLeft, Edit, Trash2 } from "lucide-react";
import { Event } from "@/types/event";
import { getEventById, deleteEvent } from "@/utils/eventStorage";
import { speakers } from "@/data/speakers";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

const EventDetail = () => {
  const [event, setEvent] = useState<Event | null>(null);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    if (id) {
      const foundEvent = getEventById(id);
      if (foundEvent) {
        setEvent(foundEvent);
      } else {
        navigate("/404");
      }
    }
  }, [id, navigate]);

  const handleDelete = () => {
    if (event) {
      deleteEvent(event.id);
      toast({
        title: "Event Deleted",
        description: `${event.title} has been deleted.`,
      });
      navigate("/events");
    }
  };

  const getStatusColor = (status: Event["status"]) => {
    switch (status) {
      case "Scheduled":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100/80";
      case "Completed":
        return "bg-green-100 text-green-800 hover:bg-green-100/80";
      case "Cancelled":
        return "bg-red-100 text-red-800 hover:bg-red-100/80";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100/80";
    }
  };

  if (!event) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-full">
          <p>Loading event details...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        <div>
          <Button variant="outline" size="sm" onClick={() => navigate('/events')} className="mb-6">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Events
          </Button>
          <div className="flex flex-col md:flex-row justify-between md:items-start gap-4">
            <div className="flex-1">
              <Badge className={`${getStatusColor(event.status)} mb-2`}>{event.status}</Badge>
              <h1 className="text-4xl font-bold tracking-tight text-foreground">{event.title}</h1>
              <div className="flex flex-wrap items-center gap-x-6 gap-y-2 mt-3 text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>{format(new Date(event.date), "PPP")}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5" />
                  <span>{event.startTime} - {event.endTime}</span>
                </div>
                {event.location && (
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>{event.location}</span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0 mt-2 md:mt-0">
                <Button variant="outline" size="icon" onClick={() => navigate(`/events?edit=${event.id}`)}>
                    <Edit className="h-4 w-4" />
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="icon">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the event "{event.title}".
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
            <div className="md:col-span-2 space-y-6">
              {event.description && (
                <Card>
                  <CardHeader>
                    <CardTitle>About this event</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground whitespace-pre-wrap">{event.description}</p>
                  </CardContent>
                </Card>
              )}
            </div>
            
            <div className="space-y-6">
              {event.speakerIds.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      Speakers
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {event.speakerIds.map((speakerId) => {
                      const speaker = speakers.find((s) => s.id === speakerId);
                      return speaker ? (
                        <div key={speaker.id} className="flex items-center space-x-3 group cursor-pointer p-2 -m-2 rounded-md hover:bg-accent" onClick={() => navigate(`/speaker/${speaker.id}`)}>
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={speaker.image} alt={speaker.name} />
                            <AvatarFallback>{speaker.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-sm group-hover:text-primary transition-colors">{speaker.name}</p>
                            <p className="text-xs text-muted-foreground">{speaker.category}</p>
                          </div>
                        </div>
                      ) : null;
                    })}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
          
          {event.speakerIds.length === 0 && !event.description && (
              <Card>
                  <CardContent>
                      <p className="text-muted-foreground text-center py-12">No additional details for this event.</p>
                  </CardContent>
              </Card>
          )}

      </div>
    </Layout>
  );
};

export default EventDetail;
