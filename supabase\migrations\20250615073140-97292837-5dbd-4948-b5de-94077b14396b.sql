
-- ========= EMPLOYEES MIGRATION =========
-- Clear existing data and seed the employees table
TRUNCATE public.employees RESTART IDENTITY CASCADE;

INSERT INTO public.employees (name, email, role, image_url) VALUES
('<PERSON>', '<EMAIL>', 'CEO', 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&h=400&fit=crop&crop=face'),
('<PERSON>', '<EMAIL>', 'CTO', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'),
('<PERSON>', '<EMAIL>', 'Lead Developer', 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face');

-- ========= SPEAKER GALLERY MIGRATION =========
-- Create a table for speaker gallery images
CREATE TABLE IF NOT EXISTS public.speaker_images (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  speaker_id UUID NOT NULL REFERENCES public.speakers(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- ========= STORAGE BUCKETS & POLICIES =========
-- Create storage buckets if they don't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types) VALUES
('employee-avatars', 'employee-avatars', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/gif']),
('speaker-avatars', 'speaker-avatars', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/gif']),
('speaker-gallery', 'speaker-gallery', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/gif'])
ON CONFLICT (id) DO UPDATE SET 
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- RLS for speaker_images table
ALTER TABLE public.speaker_images ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Allow public read access to speaker images" ON public.speaker_images;
CREATE POLICY "Allow public read access to speaker images" ON public.speaker_images FOR SELECT USING (true);
DROP POLICY IF EXISTS "Allow authenticated users to manage speaker images" ON public.speaker_images;
CREATE POLICY "Allow authenticated users to manage speaker images" ON public.speaker_images FOR ALL USING (auth.role() = 'authenticated');

-- RLS policies for storage objects
-- employee-avatars policies
DROP POLICY IF EXISTS "Allow public read access on employee-avatars" ON storage.objects;
CREATE POLICY "Allow public read access on employee-avatars" ON storage.objects FOR SELECT USING (bucket_id = 'employee-avatars');
DROP POLICY IF EXISTS "Allow authenticated insert on employee-avatars" ON storage.objects;
CREATE POLICY "Allow authenticated insert on employee-avatars" ON storage.objects FOR INSERT TO authenticated WITH CHECK (bucket_id = 'employee-avatars');
DROP POLICY IF EXISTS "Allow authenticated update on employee-avatars" ON storage.objects;
CREATE POLICY "Allow authenticated update on employee-avatars" ON storage.objects FOR UPDATE TO authenticated USING (bucket_id = 'employee-avatars');
DROP POLICY IF EXISTS "Allow authenticated delete on employee-avatars" ON storage.objects;
CREATE POLICY "Allow authenticated delete on employee-avatars" ON storage.objects FOR DELETE TO authenticated USING (bucket_id = 'employee-avatars');

-- speaker-avatars policies
DROP POLICY IF EXISTS "Allow public read access on speaker-avatars" ON storage.objects;
CREATE POLICY "Allow public read access on speaker-avatars" ON storage.objects FOR SELECT USING (bucket_id = 'speaker-avatars');
DROP POLICY IF EXISTS "Allow authenticated insert on speaker-avatars" ON storage.objects;
CREATE POLICY "Allow authenticated insert on speaker-avatars" ON storage.objects FOR INSERT TO authenticated WITH CHECK (bucket_id = 'speaker-avatars');
DROP POLICY IF EXISTS "Allow authenticated update on speaker-avatars" ON storage.objects;
CREATE POLICY "Allow authenticated update on speaker-avatars" ON storage.objects FOR UPDATE TO authenticated USING (bucket_id = 'speaker-avatars');
DROP POLICY IF EXISTS "Allow authenticated delete on speaker-avatars" ON storage.objects;
CREATE POLICY "Allow authenticated delete on speaker-avatars" ON storage.objects FOR DELETE TO authenticated USING (bucket_id = 'speaker-avatars');

-- speaker-gallery policies
DROP POLICY IF EXISTS "Allow public read access on speaker-gallery" ON storage.objects;
CREATE POLICY "Allow public read access on speaker-gallery" ON storage.objects FOR SELECT USING (bucket_id = 'speaker-gallery');
DROP POLICY IF EXISTS "Allow authenticated insert on speaker-gallery" ON storage.objects;
CREATE POLICY "Allow authenticated insert on speaker-gallery" ON storage.objects FOR INSERT TO authenticated WITH CHECK (bucket_id = 'speaker-gallery');
DROP POLICY IF EXISTS "Allow authenticated update on speaker-gallery" ON storage.objects;
CREATE POLICY "Allow authenticated update on speaker-gallery" ON storage.objects FOR UPDATE TO authenticated USING (bucket_id = 'speaker-gallery');
DROP POLICY IF EXISTS "Allow authenticated delete on speaker-gallery" ON storage.objects;
CREATE POLICY "Allow authenticated delete on speaker-gallery" ON storage.objects FOR DELETE TO authenticated USING (bucket_id = 'speaker-gallery');
