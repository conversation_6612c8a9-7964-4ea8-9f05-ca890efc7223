
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, Download, FileSpreadsheet, AlertCircle, Info } from "lucide-react";
import { generateSpeakerCsvTemplate, parseCsvToSpeakers } from "@/utils/speakerCsvExporter";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { Speaker } from "@/types/speaker";

type SpeakerInsert = Omit<Speaker, 'id' | 'created_at'>;

const SpeakerCsvImport = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewData, setPreviewData] = useState<Partial<Speaker>[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setCsvFile(file);
    
    // Preview first few rows
    const text = await file.text();
    const speakers = parseCsvToSpeakers(text);
    setPreviewData(speakers.slice(0, 3));
  };

  const handleImport = async () => {
    if (!csvFile) return;

    setIsProcessing(true);
    try {
      const text = await csvFile.text();
      const speakers = parseCsvToSpeakers(text);
      
      if (speakers.length === 0) {
        toast({
          title: "No valid data found",
          description: "Please check your CSV format and try again.",
          variant: "destructive",
        });
        return;
      }

      // Filter out speakers without required fields and transform to proper format
      const validSpeakers: SpeakerInsert[] = speakers
        .filter(speaker => speaker.name)
        .map(speaker => ({
          name: speaker.name!,
          bio: speaker.bio || null,
          category: speaker.category || null,
          location: speaker.location || null,
          experience: speaker.experience || null,
          rate: speaker.rate || null,
          specialties: speaker.specialties || null,
          availability: speaker.availability || null,
          date_of_birth: speaker.date_of_birth || null,
        }));

      if (validSpeakers.length === 0) {
        toast({
          title: "No valid speakers found",
          description: "All speakers are missing required fields (name).",
          variant: "destructive",
        });
        return;
      }

      // Import all speakers in a single batch
      const { data, error } = await supabase
        .from('speakers')
        .insert(validSpeakers)
        .select('id');

      if (error) {
        console.error('Batch insert error:', error);
        toast({
          title: "Import failed",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      const successCount = data?.length || 0;
      
      toast({
        title: "Import completed",
        description: `Successfully imported ${successCount} speakers.`,
        variant: "default",
      });

      queryClient.invalidateQueries({ queryKey: ['speakers'] });
      setIsOpen(false);
      setCsvFile(null);
      setPreviewData([]);
    } catch (error: any) {
      console.error('Import error:', error);
      toast({
        title: "Import failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <FileSpreadsheet className="h-4 w-4 mr-2" />
          Import CSV
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Import Speakers from CSV</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Download the template first to see the required format. Required fields: name.
            </AlertDescription>
          </Alert>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={generateSpeakerCsvTemplate}>
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>

          <div>
            <Label htmlFor="csv-file">Select CSV File</Label>
            <Input
              id="csv-file"
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              className="mt-2"
            />
          </div>

          {previewData.length > 0 && (
            <div>
              <Label>Preview (First 3 rows)</Label>
              <div className="mt-2 p-4 bg-muted rounded-lg">
                {previewData.map((speaker, index) => (
                  <div key={index} className="text-sm mb-2">
                    <strong>{speaker.name}</strong> - {speaker.category} - {speaker.location}
                  </div>
                ))}
              </div>
            </div>
          )}

          <Button 
            onClick={handleImport} 
            disabled={!csvFile || isProcessing}
            className="w-full"
          >
            <Upload className="h-4 w-4 mr-2" />
            {isProcessing ? 'Importing...' : 'Import Speakers'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SpeakerCsvImport;
