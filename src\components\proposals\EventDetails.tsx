
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface EventDetailsProps {
  eventDetails: {
    name: string;
    date: string;
    location: string;
    duration: string;
    audience: string;
    budget: string;
    objectives: string;
    requirements: string;
  };
  setEventDetails: (details: {
    name: string;
    date: string;
    location: string;
    duration: string;
    audience: string;
    budget: string;
    objectives: string;
    requirements: string;
  }) => void;
}

export const EventDetails = ({ eventDetails, setEventDetails }: EventDetailsProps) => {
  const handleChange = (field: keyof typeof eventDetails, value: string) => {
    setEventDetails({ ...eventDetails, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Event Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="eventName">Event Name *</Label>
          <Input
            id="eventName"
            value={eventDetails.name}
            onChange={(e) => handleChange("name", e.target.value)}
            placeholder="Enter event name"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="eventDate">Event Date</Label>
            <Input
              id="eventDate"
              type="date"
              value={eventDetails.date}
              onChange={(e) => handleChange("date", e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="budget">Budget</Label>
            <Input
              id="budget"
              value={eventDetails.budget}
              onChange={(e) => handleChange("budget", e.target.value)}
              placeholder="$50,000"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="eventLocation">Location</Label>
          <Input
            id="eventLocation"
            value={eventDetails.location}
            onChange={(e) => handleChange("location", e.target.value)}
            placeholder="Enter event location"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="duration">Duration</Label>
            <Input
              id="duration"
              value={eventDetails.duration}
              onChange={(e) => handleChange("duration", e.target.value)}
              placeholder="2 hours"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="audience">Audience</Label>
            <Input
              id="audience"
              value={eventDetails.audience}
              onChange={(e) => handleChange("audience", e.target.value)}
              placeholder="500 professionals"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="objectives">Objectives</Label>
          <Textarea
            id="objectives"
            value={eventDetails.objectives}
            onChange={(e) => handleChange("objectives", e.target.value)}
            placeholder="What are the goals of this event?"
            rows={3}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="requirements">Requirements</Label>
          <Textarea
            id="requirements"
            value={eventDetails.requirements}
            onChange={(e) => handleChange("requirements", e.target.value)}
            placeholder="Any specific requirements or constraints"
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  );
};
