
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  FileText, 
  Calendar, 
  User, 
  Edit,
  Trash2,
  Send,
  Palette
} from "lucide-react";
import { format } from "date-fns";
import { ClientInformation } from "@/components/proposals/ClientInformation";
import { EventDetails } from "@/components/proposals/EventDetails";
import SpeakerSelection from "@/components/proposals/SpeakerSelection";
import { generateProposalPDF } from "@/utils/pdfGenerator";
import { generateProposalPPTX } from "@/utils/pptxGenerator";
import { TemplateManager } from "@/components/TemplateManager";
import { Tables } from "@/integrations/supabase/types";

const Proposals = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [editingProposal, setEditingProposal] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [isGeneratingPPTX, setIsGeneratingPPTX] = useState(false);
  const [isTemplateManagerOpen, setIsTemplateManagerOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Tables<"proposal_templates"> | null>(null);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  
  // Proposal form state
  const [clientInfo, setClientInfo] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    notes: ""
  });

  const [eventDetails, setEventDetails] = useState({
    name: "",
    date: "",
    location: "",
    duration: "",
    audience: "",
    budget: "",
    objectives: "",
    requirements: ""
  });

  const [selectedSpeakers, setSelectedSpeakers] = useState<any[]>([]);

  // Fetch proposals
  const { data: proposals = [], isLoading } = useQuery({
    queryKey: ['proposals'],
    queryFn: async () => {
      const { data, error } = await supabase.from('proposals').select('*').order('created_at', { ascending: false });
      if (error) throw error;
      return data || [];
    }
  });

  // Create proposal mutation
  const createProposalMutation = useMutation({
    mutationFn: async (proposalData: any) => {
      const { data, error } = await supabase.from('proposals').insert([proposalData]).select().single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({ title: "Success", description: "Proposal created successfully!" });
      queryClient.invalidateQueries({ queryKey: ['proposals'] });
      handleCloseDialog();
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });

  // Update proposal mutation
  const updateProposalMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: any) => {
      const { data, error } = await supabase.from('proposals').update(updateData).eq('id', id).select().single();
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({ title: "Success", description: "Proposal updated successfully!" });
      queryClient.invalidateQueries({ queryKey: ['proposals'] });
      handleCloseDialog();
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });

  // Delete proposal mutation
  const deleteProposalMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from('proposals').delete().eq('id', id);
      if (error) throw error;
    },
    onSuccess: () => {
      toast({ title: "Success", description: "Proposal deleted successfully!" });
      queryClient.invalidateQueries({ queryKey: ['proposals'] });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });

  const handleCreateProposal = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
      return;
    }

    const proposalData = {
      event_name: eventDetails.name,
      details: { clientInfo, eventDetails, selectedSpeakers },
      status: 'draft',
      submitted_date: null
    };

    createProposalMutation.mutate(proposalData);
  };

  const handleUpdateProposal = () => {
    if (!editingProposal) return;

    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
      return;
    }

    const updateData = {
      id: editingProposal.id,
      event_name: eventDetails.name,
      details: { clientInfo, eventDetails, selectedSpeakers },
      status: editingProposal.status
    };

    updateProposalMutation.mutate(updateData);
  };

  const handleEditProposal = (proposal: any) => {
    setEditingProposal(proposal);
    const details = proposal.details || {};
    const parsedDetails = typeof details === 'string' ? JSON.parse(details) : details;
    
    setClientInfo(parsedDetails.clientInfo || { name: "", email: "", company: "", phone: "", notes: "" });
    setEventDetails(parsedDetails.eventDetails || { name: "", date: "", location: "", duration: "", audience: "", budget: "", objectives: "", requirements: "" });
    setSelectedSpeakers(parsedDetails.selectedSpeakers || []);
    setCurrentStep(1);
    setIsCreateOpen(true);
  };

  const handleCloseDialog = () => {
    setIsCreateOpen(false);
    setEditingProposal(null);
    setCurrentStep(1);
    setClientInfo({ name: "", email: "", company: "", phone: "", notes: "" });
    setEventDetails({ name: "", date: "", location: "", duration: "", audience: "", budget: "", objectives: "", requirements: "" });
    setSelectedSpeakers([]);
  };

  const handleGeneratePDF = async (proposal: any) => {
    setIsGeneratingPDF(true);
    try {
      const details = typeof proposal.details === 'string' ? JSON.parse(proposal.details) : proposal.details;
      
      // Convert to the format expected by the PDF generator
      const proposalData = {
        id: proposal.id,
        clientName: details.clientInfo?.name || 'Unknown Client',
        clientEmail: details.clientInfo?.email || '',
        event: {
          eventName: details.eventDetails?.name || proposal.event_name || 'Untitled Event',
          eventDate: details.eventDetails?.date || '',
          eventLocation: details.eventDetails?.location || '',
          eventType: 'Speaking Event',
          audience: details.eventDetails?.audience || '',
          budget: details.eventDetails?.budget || '',
          description: details.eventDetails?.objectives || ''
        },
        speakers: details.selectedSpeakers?.map((s: any) => ({
          speaker: s,
          role: 'Speaker',
          notes: ''
        })) || [],
        totalBudget: details.selectedSpeakers?.reduce((sum: number, s: any) => sum + (s.rate || 0), 0) || 0,
        createdAt: proposal.created_at,
        status: proposal.status || 'Draft'
      };

      await generateProposalPDF(proposalData, null);
      
      toast({
        title: "PDF Generated",
        description: "Your proposal PDF has been generated and downloaded successfully.",
      });
    } catch (error) {
      console.error('PDF Generation Error:', error);
      toast({
        title: "PDF Generation Failed",
        description: "There was an error generating the PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleGeneratePPTX = async (proposal: any) => {
    setIsGeneratingPPTX(true);
    try {
      const details = typeof proposal.details === 'string' ? JSON.parse(proposal.details) : proposal.details;
      
      // Convert to the format expected by the PPTX generator
      const proposalData = {
        id: proposal.id,
        clientName: details.clientInfo?.name || 'Unknown Client',
        clientEmail: details.clientInfo?.email || '',
        event: {
          eventName: details.eventDetails?.name || proposal.event_name || 'Untitled Event',
          eventDate: details.eventDetails?.date || '',
          eventLocation: details.eventDetails?.location || '',
          eventType: 'Speaking Event',
          audience: details.eventDetails?.audience || '',
          budget: details.eventDetails?.budget || '',
          description: details.eventDetails?.objectives || ''
        },
        speakers: details.selectedSpeakers?.map((s: any) => ({
          speaker: s,
          role: 'Speaker',
          notes: ''
        })) || [],
        totalBudget: details.selectedSpeakers?.reduce((sum: number, s: any) => sum + (s.rate || 0), 0) || 0,
        createdAt: proposal.created_at,
        status: proposal.status || 'Draft'
      };

      await generateProposalPPTX(proposalData);
      
      toast({
        title: "PPTX Generated",
        description: "Your proposal presentation has been generated and downloaded successfully.",
      });
    } catch (error) {
      console.error('PPTX Generation Error:', error);
      toast({
        title: "PPTX Generation Failed",
        description: "There was an error generating the presentation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPPTX(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <ClientInformation
            clientInfo={clientInfo}
            setClientInfo={setClientInfo}
          />
        );
      case 2:
        return (
          <EventDetails
            eventDetails={eventDetails}
            setEventDetails={setEventDetails}
          />
        );
      case 3:
        return (
          <SpeakerSelection
            selectedSpeakers={selectedSpeakers}
            setSelectedSpeakers={setSelectedSpeakers}
          />
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileText className="h-12 w-12 animate-pulse text-muted-foreground mx-auto mb-4" />
            <p>Loading proposals...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Proposals</h1>
            <p className="text-muted-foreground">Manage and create speaker proposals</p>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsTemplateManagerOpen(true)}
            >
              <Palette className="h-4 w-4 mr-2" />
              Manage Templates
            </Button>
            
            <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => setShowTemplateSelector(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Proposal
                </Button>
              </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingProposal ? 'Edit Proposal' : 'Create New Proposal'}
                </DialogTitle>
              </DialogHeader>
              
              {/* Step indicators */}
              <div className="flex items-center justify-center space-x-4 mb-6">
                {[1, 2, 3].map((step) => (
                  <div
                    key={step}
                    className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                      currentStep >= step
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    }`}
                  >
                    {step}
                  </div>
                ))}
              </div>

              {renderStepContent()}

              <div className="flex justify-between pt-4">
                <Button
                  variant="outline"
                  onClick={() => currentStep > 1 ? setCurrentStep(currentStep - 1) : handleCloseDialog()}
                >
                  {currentStep > 1 ? 'Previous' : 'Cancel'}
                </Button>
                <Button
                  onClick={editingProposal ? handleUpdateProposal : handleCreateProposal}
                  disabled={createProposalMutation.isPending || updateProposalMutation.isPending}
                >
                  {currentStep < 3 ? 'Next' : editingProposal ? 'Update Proposal' : 'Create Proposal'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          </div>
        </div>

        {/* Template Manager Dialog */}
        <TemplateManager 
          open={isTemplateManagerOpen} 
          onOpenChange={setIsTemplateManagerOpen}
          mode="manage"
        />

        {/* Template Selector Dialog */}
        <TemplateManager
          open={showTemplateSelector}
          onOpenChange={setShowTemplateSelector}
          mode="select"
          onSelectTemplate={(template) => {
            setSelectedTemplate(template);
            setShowTemplateSelector(false);
            setIsCreateOpen(true);
          }}
        />

        {/* Proposals List */}
        <div className="grid gap-6">
          {proposals.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No proposals yet</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Get started by creating your first speaker proposal
                </p>
                <Button onClick={() => setIsCreateOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Proposal
                </Button>
              </CardContent>
            </Card>
          ) : (
            proposals.map((proposal) => {
              const details = proposal.details || {};
              const parsedDetails = typeof details === 'string' ? JSON.parse(details) : details;
              
              return (
                <Card key={proposal.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <CardTitle className="text-xl">
                          {proposal.event_name || 'Untitled Proposal'}
                        </CardTitle>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>Created {format(new Date(proposal.created_at), 'MMM d, yyyy')}</span>
                          </div>
                          {parsedDetails.selectedSpeakers?.length > 0 && (
                            <div className="flex items-center space-x-1">
                              <User className="h-4 w-4" />
                              <span>{parsedDetails.selectedSpeakers.length} Speaker{parsedDetails.selectedSpeakers.length > 1 ? 's' : ''}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <Badge className={getStatusColor(proposal.status || 'draft')}>
                        {proposal.status || 'draft'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {parsedDetails.clientInfo?.company && (
                        <div>
                          <span className="font-medium">Company: </span>
                          <span className="text-muted-foreground">{parsedDetails.clientInfo.company}</span>
                        </div>
                      )}
                      {parsedDetails.eventDetails?.location && (
                        <div>
                          <span className="font-medium">Location: </span>
                          <span className="text-muted-foreground">{parsedDetails.eventDetails.location}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between pt-4 border-t">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditProposal(proposal)}
                            className="flex items-center space-x-1"
                          >
                            <Edit className="h-4 w-4" />
                            <span>Edit</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteProposalMutation.mutate(proposal.id)}
                            disabled={deleteProposalMutation.isPending}
                            className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Delete</span>
                          </Button>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          {proposal.status === 'draft' && (
                            <Button size="sm" variant="outline">
                              <Send className="h-4 w-4 mr-1" />
                              Send
                            </Button>
                          )}
                          
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleGeneratePDF(proposal)}
                            disabled={isGeneratingPDF}
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            {isGeneratingPDF ? 'Generating...' : 'Generate PDF'}
                          </Button>
                          
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleGeneratePPTX(proposal)}
                            disabled={isGeneratingPPTX}
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            {isGeneratingPPTX ? 'Generating...' : 'Generate PPTX'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Proposals;
