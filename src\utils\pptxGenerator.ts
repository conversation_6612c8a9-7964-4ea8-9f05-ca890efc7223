
import PptxGenJS from 'pptxgenjs';
import { Proposal } from '../types/proposal';

const toDataURL = (url: string): Promise<string> =>
  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }
      return response.blob();
    })
    .then(blob => new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    }));

const theme = {
    colors: {
      primary: '0D47A1',
      secondary: '1976D2',
      accent: 'FFC107',
      textPrimary: '212121',
      textSecondary: '757575',
      background: 'FFFFFF',
      lightGray: 'F5F5F5',
    },
    fonts: {
      sans: 'Arial',
    }
};

export const generateProposalPPTX = async (proposal: Proposal) => {
    try {
        const pptx = new PptxGenJS();
        
        pptx.layout = 'LAYOUT_WIDE';

        // Slide Master with footer
        pptx.defineSlideMaster({
            title: 'MASTER_SLIDE',
            background: { color: theme.colors.background },
            objects: [
                {
                    'rect': { x: 0, y: '95%', w: '100%', h: '5%', fill: { color: theme.colors.lightGray } }
                },
                {
                    'text': {
                        text: 'Speaker Agency | Professional Proposals',
                        options: { x: 0.5, y: '95.5%', w: '40%', fontFace: theme.fonts.sans, fontSize: 10, color: theme.colors.textSecondary }
                    }
                },
            ],
        });

        // Page 1: Cover Page
        const coverSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
        coverSlide.addText('Speaker Proposal', { 
            x: '10%', y: '30%', w: '80%', 
            align: 'center', fontSize: 24, color: theme.colors.textSecondary
        });
        coverSlide.addText(proposal.event.eventName, {
            x: '10%', y: '40%', w: '80%',
            align: 'center', fontSize: 48, bold: true, color: theme.colors.primary,
        });
        coverSlide.addText(`Prepared for: ${proposal.clientName}`, {
            x: '10%', y: '55%', w: '80%',
            align: 'center', fontSize: 20, color: theme.colors.textPrimary,
        });
        coverSlide.addText(new Date(proposal.createdAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }), {
            x: '10%', y: '65%', w: '80%',
            align: 'center', fontSize: 16, color: theme.colors.textSecondary,
        });

        // Page 2: About Us
        const aboutUsSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
        aboutUsSlide.addText('About Speaker Agency', { x: 0.5, y: 0.25, w: '90%', h: 0.75, fontSize: 36, bold: true, color: theme.colors.primary });
        aboutUsSlide.addShape(pptx.ShapeType.line, { x: 0.5, y: 1.0, w: 9, h: 0, line: { color: theme.colors.primary, width: 1.5 } });
        
        aboutUsSlide.addText('Our Mission', { x: 0.5, y: 1.2, w: '90%', h: 0.5, fontSize: 24, bold: true, color: theme.colors.secondary });
        aboutUsSlide.addText(
            `At Speaker Agency, we connect world-class speakers with organizations seeking exceptional thought leadership and inspiration. Our mission is to deliver transformative experiences that educate, motivate, and drive meaningful change.`,
            { x: 0.5, y: 1.8, w: '90%', h: 1.5, fontSize: 16, color: theme.colors.textSecondary, paraSpaceAfter: 10 }
        );
        
        const statsY = 4.0;
        aboutUsSlide.addText('500+', { x: 1, y: statsY, w: 2.5, h: 1, align: 'center', fontSize: 48, bold: true, color: theme.colors.primary });
        aboutUsSlide.addText('Expert Speakers', { x: 1, y: statsY + 0.7, w: 2.5, h: 0.5, align: 'center', fontSize: 14, color: theme.colors.textSecondary });
        aboutUsSlide.addText('1000+', { x: 4, y: statsY, w: 2.5, h: 1, align: 'center', fontSize: 48, bold: true, color: theme.colors.primary });
        aboutUsSlide.addText('Successful Events', { x: 4, y: statsY + 0.7, w: 2.5, h: 0.5, align: 'center', fontSize: 14, color: theme.colors.textSecondary });
        aboutUsSlide.addText('98%', { x: 7, y: statsY, w: 2.5, h: 1, align: 'center', fontSize: 48, bold: true, color: theme.colors.primary });
        aboutUsSlide.addText('Client Satisfaction', { x: 7, y: statsY + 0.7, w: 2.5, h: 0.5, align: 'center', fontSize: 14, color: theme.colors.textSecondary });

        // Page 3: Event Details
        const eventSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
        eventSlide.addText('Event Overview', { x: 0.5, y: 0.25, w: '90%', h: 0.75, fontSize: 36, bold: true, color: theme.colors.primary });
        eventSlide.addShape(pptx.ShapeType.line, { x: 0.5, y: 1.0, w: 9, h: 0, line: { color: theme.colors.primary, width: 1.5 } });
        eventSlide.addText(proposal.event.eventName, { x: 0.5, y: 1.2, w: '90%', h: 0.5, fontSize: 24, bold: true, color: theme.colors.secondary });
        eventSlide.addText(proposal.event.description, { x: 0.5, y: 1.8, w: '90%', h: 1.5, fontSize: 16, color: theme.colors.textSecondary });

        const eventDetailsTable = [
            [{ text: 'Date:', options: { bold: true, color: theme.colors.textPrimary } }, { text: new Date(proposal.event.eventDate).toLocaleDateString(), options: { color: theme.colors.textSecondary } }],
            [{ text: 'Location:', options: { bold: true, color: theme.colors.textPrimary } }, { text: proposal.event.eventLocation, options: { color: theme.colors.textSecondary } }],
            [{ text: 'Event Type:', options: { bold: true, color: theme.colors.textPrimary } }, { text: proposal.event.eventType, options: { color: theme.colors.textSecondary } }],
            [{ text: 'Audience Size:', options: { bold: true, color: theme.colors.textPrimary } }, { text: proposal.event.audience, options: { color: theme.colors.textSecondary } }],
            [{ text: 'Budget Range:', options: { bold: true, color: theme.colors.textPrimary } }, { text: proposal.event.budget, options: { color: theme.colors.textSecondary } }],
        ];
        eventSlide.addTable(eventDetailsTable, { x: 0.5, y: 3.5, w: '90%', colW: [2.5, 6.5], border: { type: 'none' }, fontSize: 14 });

        // Page 4+: Speakers
        for (const { speaker, notes } of proposal.speakers) {
            const speakerSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
            speakerSlide.addText('Proposed Speaker', { x: 0.5, y: 0.25, w: '90%', h: 0.75, fontSize: 36, bold: true, color: theme.colors.primary });
            speakerSlide.addShape(pptx.ShapeType.line, { x: 0.5, y: 1.0, w: 9, h: 0, line: { color: theme.colors.primary, width: 1.5 } });

            // Handle speaker image - use placeholder if no image available
            try {
                if (speaker.image) {
                    const imageUrl = speaker.image.startsWith('http') || speaker.image.startsWith('data:')
                        ? speaker.image
                        : new URL(speaker.image, window.location.origin).href;
                    const imageBase64 = await toDataURL(imageUrl);
                    speakerSlide.addImage({ data: imageBase64, x: 0.5, y: 1.2, w: 3, h: 3, sizing: { type: 'cover', w: 3, h: 3 } });
                } else {
                    // Add placeholder for missing image
                    speakerSlide.addShape(pptx.ShapeType.rect, { 
                        x: 0.5, y: 1.2, w: 3, h: 3, 
                        fill: { color: theme.colors.lightGray }, 
                        line: { color: theme.colors.textSecondary, width: 1 }
                    });
                    speakerSlide.addText("No Image", { 
                        x: 0.5, y: 2.5, w: 3, h: 0.5, 
                        align: 'center', valign: 'middle', 
                        color: theme.colors.textSecondary, fontSize: 14 
                    });
                }
            } catch (error) {
                console.error("Could not add speaker image to PPTX:", error);
                speakerSlide.addShape(pptx.ShapeType.rect, { 
                    x: 0.5, y: 1.2, w: 3, h: 3, 
                    fill: { color: theme.colors.lightGray }, 
                    line: { color: theme.colors.textSecondary, width: 1 }
                });
                speakerSlide.addText("Image not available", { 
                    x: 0.5, y: 2.5, w: 3, h: 0.5, 
                    align: 'center', valign: 'middle', 
                    color: theme.colors.textSecondary, fontSize: 12 
                });
            }

            speakerSlide.addText(speaker.name, { x: 0.5, y: 4.4, w: 3, h: 0.5, align: 'center', fontSize: 22, bold: true, color: theme.colors.secondary });
            speakerSlide.addText(speaker.category || 'Speaker', { x: 0.5, y: 4.8, w: 3, h: 0.5, align: 'center', color: theme.colors.textSecondary });

            speakerSlide.addText('Biography', { x: 4, y: 1.2, w: '55%', h: 0.5, fontSize: 20, bold: true, color: theme.colors.secondary });
            speakerSlide.addText(speaker.bio || 'No biography available', { x: 4, y: 1.7, w: '55%', h: 2.0, fontSize: 12, color: theme.colors.textSecondary });

            speakerSlide.addText('Key Topics', { x: 4, y: 3.8, w: '55%', h: 0.5, fontSize: 20, bold: true, color: theme.colors.secondary });
            const topics = (speaker.specialties || ['General Speaking']).slice(0, 6).map(s => ({ text: s, options: { bullet: true } }));
            speakerSlide.addText(topics, { x: 4, y: 4.3, w: '55%', h: 2, fontSize: 12, color: theme.colors.textSecondary });
            
            if (notes) {
                speakerSlide.addText('Notes for this event:', { x: 0.5, y: 5.5, w: '90%', h: 0.3, bold: true, fontSize: 14 });
                speakerSlide.addText(notes, { x: 0.5, y: 5.8, w: '90%', h: 0.5, fontSize: 12, color: theme.colors.textSecondary });
            }
        }

        // Page: Investment
        const investmentSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
        investmentSlide.addText('Investment Summary', { x: 0.5, y: 0.25, w: '90%', h: 0.75, fontSize: 36, bold: true, color: theme.colors.primary });
        investmentSlide.addShape(pptx.ShapeType.line, { x: 0.5, y: 1.0, w: 9, h: 0, line: { color: theme.colors.primary, width: 1.5 } });
        investmentSlide.addText('Speaker Fees', { x: 0.5, y: 1.2, w: '90%', h: 0.5, fontSize: 24, bold: true, color: theme.colors.secondary });

        const tableHeader = [
            { text: 'Speaker', options: { bold: true, fill: { color: theme.colors.lightGray }, color: theme.colors.textPrimary } },
            { text: 'Fee', options: { bold: true, align: 'right' as const, fill: { color: theme.colors.lightGray }, color: theme.colors.textPrimary } }
        ];
        const tableRows = proposal.speakers.map(({ speaker }) => [
            { text: speaker.name, options: { color: theme.colors.textPrimary } },
            { text: `$${(speaker.rate || 0).toLocaleString()}`, options: { align: 'right' as const, color: theme.colors.textPrimary } }
        ]);
        const totalBudget = proposal.speakers.reduce((sum, { speaker }) => sum + (speaker.rate || 0), 0);
        const tableFooter = [
            { text: 'Total Investment', options: { bold: true, fontSize: 16, color: theme.colors.primary } },
            { text: `$${totalBudget.toLocaleString()}`, options: { bold: true, fontSize: 18, color: theme.colors.primary, align: 'right' as const } }
        ];

        investmentSlide.addTable([tableHeader, ...tableRows, tableFooter], { 
            x: 0.5, y: 1.8, w: '90%', 
            colW: [7, 2.5], 
            border: { type: 'solid', pt: 1, color: "E0E0E0" },
            valign: 'middle',
            rowH: 0.5,
            fontSize: 14
        });
        
        investmentSlide.addText('Terms & Conditions', { x: 0.5, y: 4.5, w: '90%', h: 0.5, fontSize: 16, bold: true, color: theme.colors.secondary });
        const terms = [
            'Fees are inclusive of all preparation and presentation time. Travel and accommodation expenses are additional unless otherwise specified.',
            'A 50% deposit is required upon confirmation to secure the speaker(s).',
            'The remaining balance is due 14 days prior to the event date.',
            'This proposal is valid for 30 days.'
        ];
        investmentSlide.addText(terms.map(t => ({ text: t, options: { bullet: {type: 'bullet'} } })), {
            x: 0.5, y: 5.0, w: '90%', h: 1.5, fontSize: 10, color: theme.colors.textSecondary
        });

        // Page: Thank you
        const thankYouSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
        thankYouSlide.addText('Thank You', { x: '10%', y: '20%', w: '80%', h: '15%', align: 'center', fontSize: 42, bold: true, color: theme.colors.primary });
        thankYouSlide.addText(`We are excited about the possibility of partnering with you for ${proposal.event.eventName}. Please let us know if you have any questions.`, {
            x: '15%', y: '40%', w: '70%', h: '20%', align: 'center', fontSize: 18, color: theme.colors.textSecondary
        });
        
        thankYouSlide.addText(
            `Next Steps:\n1. Review: Discuss this proposal with your team.\n2. Connect: Schedule a call with us to finalize details.\n3. Confirm: Sign the agreement and we'll secure your speakers!`,
            { x: '25%', y: '65%', w: '50%', h: '20%', align: 'center', fontSize: 14, color: theme.colors.textPrimary, lineSpacing: 22 }
        );

        const fileName = `speaker-proposal-${proposal.event.eventName.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}.pptx`;
        await pptx.writeFile({ fileName });
    } catch (error) {
        console.error("Error during PPTX generation:", error);
        throw error;
    }
};
