
-- Create a table to store Bitrix deal information and sync status
CREATE TABLE public.bitrix_deals (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  bitrix_deal_id TEXT NOT NULL UNIQUE,
  deal_title TEXT NOT NULL,
  client_name TEXT,
  client_email TEXT,
  deal_amount DECIMAL,
  deal_stage TEXT,
  event_date TIMESTAMP WITH TIME ZONE,
  event_location TEXT,
  speaker_requirements TEXT,
  synced_to_event_id UUID REFERENCES public.events(id),
  sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add Row Level Security
ALTER TABLE public.bitrix_deals ENABLE ROW LEVEL SECURITY;

-- Create policies for bitrix_deals
CREATE POLICY "Allow all operations on bitrix_deals" 
  ON public.bitrix_deals 
  FOR ALL 
  USING (true);

-- Create an index for faster lookups
CREATE INDEX idx_bitrix_deals_bitrix_id ON public.bitrix_deals(bitrix_deal_id);
CREATE INDEX idx_bitrix_deals_sync_status ON public.bitrix_deals(sync_status);
